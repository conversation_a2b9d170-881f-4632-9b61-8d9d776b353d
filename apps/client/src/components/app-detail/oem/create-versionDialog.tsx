import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import type { BaseVersion } from '../types'
import { CreateClientVersionSchema, type CreateClientVersionInput } from '@coozf/zod'

interface CreateVersionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  applicationId: string
  baseVersions: BaseVersion
  onSave: (data: CreateClientVersionInput) => void
}

export function CreateVersionDialog({
  open,
  onOpenChange,
  applicationId,
  baseVersions,
  onSave,
}: CreateVersionDialogProps) {
  const form = useForm({
    resolver: zodResolver(CreateClientVersionSchema),
    defaultValues: {
      baseVersionId: '',
      // platform: 'WIN' as const,
      description: '',
    },
  })

  const onSubmit = (data: any) => {
    const { gitlabProjectId, ...createVersionData } = data
    const createData: CreateClientVersionInput = {
      applicationId,
      ...createVersionData,
    }
    onSave(createData)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>创建客户端版本</DialogTitle>
          <DialogDescription>基于公用版本创建您的定制客户端</DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="baseVersionId">基础版本 *</Label>
            <select
              id="baseVersionId"
              {...form.register('baseVersionId')}
              className="w-full px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="">请选择基础版本</option>
              {baseVersions ? (
                <option key={baseVersions.id} value={baseVersions.id}>
                  {baseVersions.version} ({baseVersions.type})
                </option>
              ) : (
                <option value="">暂无可用版本</option>
              )}
            </select>
            {form.formState.errors.baseVersionId && (
              <p className="text-sm text-destructive">{form.formState.errors.baseVersionId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">版本描述</Label>
            <Input id="description" {...form.register('description')} placeholder="请输入版本描述" />
            {form.formState.errors.description && (
              <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
            )}
          </div>

          {/* <div className="space-y-2">
            <Label htmlFor="platform">平台 *</Label>
            <select
              id="platform"
              {...form.register('platform')}
              className="w-full px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="WIN">Windows</option>
              <option value="MAC">macOS</option>
            </select>
            {form.formState.errors.platform && (
              <p className="text-sm text-destructive">{form.formState.errors.platform.message}</p>
            )}
          </div> */}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit">创建版本</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
