{"manifest_version": 3, "name": "Chrome Extension Starter", "description": "Chrome extension starter template with React, TypeScript, Vite and Tailwind", "version": "1.0", "options_ui": {"page": "options.html", "open_in_tab": true}, "action": {"default_icon": "icon.png", "default_popup": "popup.html"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content_script.js"], "run_at": "document_start"}], "commands": {"Ctrl+Q": {"suggested_key": {"default": "Ctrl+Q", "mac": "Command+Q"}, "description": "Ctrl+Q."}}, "background": {"type": "module", "service_worker": "service_worker.js"}, "permissions": ["storage", "tabs", "webNavigation", "tabGroups", "cookies", "webRequest", "scripting"], "host_permissions": ["<all_urls>"]}