// background.ts
console.log('Simple Session Box Background 启动')

// 类型定义
interface SessionCookie {
  name: string
  value: string
  domain: string
  path: string
  secure: boolean
  httpOnly: boolean
  sameSite: chrome.cookies.SameSiteStatus
  expirationDate?: number
}

interface LocalStorageData {
  [key: string]: string
}

interface SessionData {
  sessionId: string
  name: string
  cookies: SessionCookie[]
  localStorage: LocalStorageData
  createdAt: number
  lastAccessed: number
  domain: string
}

interface MessageRequest {
  action:
    | 'getSessionInfo'
    | 'saveSession'
    | 'restoreSession'
    | 'deleteSession'
    | 'listSessions'
    | 'getLocalStorage'
  sessionName?: string
  sessionId?: string
  localStorageData?: LocalStorageData
}

interface MessageResponse {
  sessionId?: string
  tabId?: number
  success?: boolean
  error?: string
  sessions?: Array<{
    id: string
    name: string
    domain: string
    createdAt: number
    lastAccessed: number
  }>
  localStorageData?: LocalStorageData
}

// 会话管理类
class SessionManager {
  private currentTabId: number | null = null

  constructor() {
    this.initializeEventListeners()
  }

  private initializeEventListeners(): void {
    // 键盘快捷键处理
    chrome.commands.onCommand.addListener(this.handleCommand.bind(this))

    // 标签页事件监听
    chrome.tabs.onActivated.addListener(this.handleTabActivated.bind(this))

    // 消息处理
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this))
  }

  private handleCommand(command: string): void {
    console.log('快捷键触发:', command)
    if (command === 'reload_extension') {
      chrome.runtime.reload()
    }
  }

  private handleTabActivated(activeInfo: chrome.tabs.TabActiveInfo): void {
    this.currentTabId = activeInfo.tabId
  }

  private extractDomain(url: string): string {
    const urlObj = new URL(url)
    return urlObj.hostname
  }

  private async getCurrentTab(): Promise<chrome.tabs.Tab> {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
    return tab
  }

  private getCurrentCookies(domain: string) {
    return chrome.cookies.getAll({ domain })
  }

  private async saveSessionData(
    sessionName: string,
    domain: string,
    cookies: chrome.cookies.Cookie[],
    localStorageData: LocalStorageData
  ): Promise<string> {
    const sessionId = `session_${domain}_${Date.now()}`

    const sessionCookies: SessionCookie[] = cookies.map((cookie) => ({
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite,
      expirationDate: cookie.expirationDate
    }))

    const sessionData: SessionData = {
      sessionId,
      name: sessionName,
      cookies: sessionCookies,
      localStorage: localStorageData,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      domain
    }

    await chrome.storage.local.set({ [sessionId]: sessionData })
    console.log(`保存会话: ${sessionName} (${sessionId})`)
    return sessionId
  }

  private async restoreSessionData(sessionId: string): Promise<void> {
    const result = await chrome.storage.local.get(sessionId)
    const sessionData = result[sessionId] as SessionData

    if (!sessionData) {
      throw new Error('会话不存在')
    }

    const tab = await this.getCurrentTab()
    if (!tab || !tab.url) {
      throw new Error('无法获取当前标签页')
    }

    const currentDomain = this.extractDomain(tab.url)
    if (!currentDomain) {
      throw new Error('无法解析当前域名')
    }

    // 检查域名是否匹配
    if (sessionData.domain !== currentDomain) {
      throw new Error(`会话域名 (${sessionData.domain}) 与当前页面域名 (${currentDomain}) 不匹配`)
    }

    // 清除当前cookies
    const currentCookies = await this.getCurrentCookies(currentDomain)
    await this.clearCurrentCookies(currentCookies)

    // 恢复cookies
    await this.setSessionCookies(sessionData.cookies)

    // 恢复localStorage (通过content script)
    if (tab.id) {
      await chrome.tabs.sendMessage(tab.id, {
        action: 'restoreLocalStorage',
        localStorageData: sessionData.localStorage
      })
    }

    // 更新最后访问时间
    sessionData.lastAccessed = Date.now()
    await chrome.storage.local.set({ [sessionId]: sessionData })

    console.log(`恢复会话: ${sessionData.name}`)
  }

  private async clearCurrentCookies(cookies: chrome.cookies.Cookie[]): Promise<void> {
    const clearPromises = cookies.map(async (cookie) => {
      try {
        await chrome.cookies.remove({
          url: this.buildCookieUrl(cookie),
          name: cookie.name
        })
      } catch (error) {
        console.warn('删除cookie失败:', error)
      }
    })

    await Promise.all(clearPromises)
  }

  private async setSessionCookies(sessionCookies: SessionCookie[]): Promise<void> {
    const setPromises = sessionCookies.map(async (cookieData) => {
      try {
        await chrome.cookies.set({
          url: this.buildCookieUrl(cookieData),
          name: cookieData.name,
          value: cookieData.value,
          domain: cookieData.domain,
          path: cookieData.path,
          secure: cookieData.secure,
          httpOnly: cookieData.httpOnly,
          sameSite: cookieData.sameSite,
          expirationDate: cookieData.expirationDate
        })
      } catch (error) {
        console.warn('设置cookie失败:', error)
      }
    })

    await Promise.all(setPromises)
  }

  private buildCookieUrl(cookie: SessionCookie): string {
    return `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`
  }

  private async listSessions(
    domain?: string
  ): Promise<
    Array<{ id: string; name: string; domain: string; createdAt: number; lastAccessed: number }>
  > {
    const allData = await chrome.storage.local.get()
    const sessions = []

    for (const [key, value] of Object.entries(allData)) {
      if (key.startsWith('session_') && typeof value === 'object' && value !== null) {
        const sessionData = value as SessionData
        if (!domain || sessionData.domain === domain) {
          sessions.push({
            id: key,
            name: sessionData.name,
            domain: sessionData.domain,
            createdAt: sessionData.createdAt,
            lastAccessed: sessionData.lastAccessed
          })
        }
      }
    }

    return sessions.sort((a, b) => b.lastAccessed - a.lastAccessed)
  }

  private async deleteSession(sessionId: string): Promise<void> {
    await chrome.storage.local.remove(sessionId)
    console.log(`删除会话: ${sessionId}`)
  }

  private handleMessage(
    request: MessageRequest & { tab: { id: string; url: string } },
    sender: chrome.runtime.MessageSender,
    sendResponse: (response: MessageResponse) => void
  ): boolean {
    console.log('收到消息:', request)

    switch (request.action) {
      case 'getSessionInfo':
        this.handleGetSessionInfo(sender, sendResponse)
        break
      case 'saveSession':
        void this.handleSaveSession(request, sendResponse)
        break
      case 'restoreSession':
        void this.handleRestoreSession(request, sendResponse)
        break
      case 'deleteSession':
        void this.handleDeleteSession(request, sendResponse)
        break
      case 'listSessions':
        void this.handleListSessions(request, sendResponse)
        break
      default:
        sendResponse({ success: false, error: 'Unknown action' })
    }

    return true // 保持消息通道开放
  }

  private handleGetSessionInfo(
    sender: chrome.runtime.MessageSender,
    sendResponse: (response: MessageResponse) => void
  ): void {
    const tabId = sender.tab?.id
    const url = sender.tab?.url

    sendResponse({
      tabId,
      success: true,
      sessionId: url ? this.extractDomain(url) || undefined : undefined
    })
  }

  private async handleSaveSession(
    request: MessageRequest & { tab: { id: string; url: string } },
    sendResponse: (response: MessageResponse) => void
  ): Promise<void> {
    try {
      const tab = request.tab

      console.log('sender', tab)
      if (!tab || !tab.url) {
        throw new Error('无法获取标签页信息')
      }

      const domain = this.extractDomain(tab.url)
      if (!domain) {
        throw new Error('无法解析域名')
      }

      const sessionName = request.sessionName || `会话_${new Date().toLocaleString()}`

      const cookies = await this.getCurrentCookies(domain)

      const localStorageData = request.localStorageData || {}

      const sessionId = await this.saveSessionData(sessionName, domain, cookies, localStorageData)

      sendResponse({ success: true, sessionId })
    } catch (error) {
      console.error('保存会话失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  private async handleRestoreSession(
    request: MessageRequest,
    sendResponse: (response: MessageResponse) => void
  ): Promise<void> {
    try {
      if (!request.sessionId) {
        throw new Error('缺少会话ID')
      }

      await this.restoreSessionData(request.sessionId)
      sendResponse({ success: true })
    } catch (error) {
      console.error('恢复会话失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  private async handleDeleteSession(
    request: MessageRequest,
    sendResponse: (response: MessageResponse) => void
  ): Promise<void> {
    try {
      if (!request.sessionId) {
        throw new Error('缺少会话ID')
      }

      await this.deleteSession(request.sessionId)
      sendResponse({ success: true })
    } catch (error) {
      console.error('删除会话失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  private async handleListSessions(
    _request: MessageRequest,
    sendResponse: (response: MessageResponse) => void
  ): Promise<void> {
    try {
      const tab = await this.getCurrentTab()
      const domain = tab?.url ? this.extractDomain(tab.url) : undefined

      const sessions = await this.listSessions(domain)
      sendResponse({ success: true, sessions })
    } catch (error) {
      console.error('获取会话列表失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  // 清理过期会话的方法
  async cleanupExpiredSessions(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const allData = await chrome.storage.local.get()
      const now = Date.now()
      const keysToRemove: string[] = []

      for (const [key, value] of Object.entries(allData)) {
        if (key.startsWith('session_') && typeof value === 'object' && value !== null) {
          const sessionData = value as SessionData
          if (sessionData.lastAccessed && now - sessionData.lastAccessed > maxAge) {
            keysToRemove.push(key)
          }
        }
      }

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove)
        console.log(`清理了 ${keysToRemove.length} 个过期会话`)
      }
    } catch (error) {
      console.error('清理过期会话失败:', error)
    }
  }
}

new SessionManager()
