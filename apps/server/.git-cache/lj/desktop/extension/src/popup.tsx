import React, { useState, useEffect, useCallback } from 'react'
import { createRoot } from 'react-dom/client'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Placeholder = any
// 类型定义
interface StatusState {
  message: string
  type: 'success' | 'error' | 'info'
  visible: boolean
}

interface SessionInfo {
  id: string
  name: string
  domain: string
  createdAt: number
  lastAccessed: number
}

interface TabInfo {
  id?: number
  url?: string
  title?: string
}

// Chrome API 类型扩展
declare global {
  interface Window {
    chrome: typeof chrome
  }
}

// 状态消息组件
interface StatusMessageProps {
  message: string
  type: 'success' | 'error' | 'info'
  visible: boolean
}

const StatusMessage: React.FC<StatusMessageProps> = ({ message, type, visible }) => {
  if (!visible) return null

  const statusStyle: React.CSSProperties = {
    textAlign: 'center',
    padding: '8px',
    marginTop: '8px',
    borderRadius: '4px',
    fontSize: '12px',
    backgroundColor: type === 'success' ? '#e8f5e8' : type === 'error' ? '#ffebee' : '#e3f2fd',
    color: type === 'success' ? '#2e7d32' : type === 'error' ? '#c62828' : '#1565c0'
  }

  return <div style={statusStyle}>{message}</div>
}

// 域名信息组件
interface DomainInfoProps {
  domain: string
  loading: boolean
}

const DomainInfo: React.FC<DomainInfoProps> = ({ domain, loading }) => {
  const domainInfoStyle: React.CSSProperties = {
    background: '#f5f5f5',
    padding: '12px',
    borderRadius: '8px',
    marginBottom: '16px'
  }

  const domainStyle: React.CSSProperties = {
    fontFamily: 'monospace',
    fontSize: '12px',
    color: '#666',
    wordBreak: 'break-all',
    marginTop: '4px'
  }

  return (
    <div style={domainInfoStyle}>
      <div>
        <strong>当前域名:</strong>
      </div>
      <div style={domainStyle} title={domain}>
        {loading ? '获取中...' : domain || '未检测到域名'}
      </div>
    </div>
  )
}

// 会话列表组件
interface SessionListProps {
  sessions: SessionInfo[]
  onRestore: (sessionId: string) => void
  onDelete: (sessionId: string) => void
  loading: boolean
}

const SessionList: React.FC<SessionListProps> = ({ sessions, onRestore, onDelete, loading }) => {
  if (loading) {
    return <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>加载中...</div>
  }

  if (sessions.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
        当前域名暂无保存的会话
      </div>
    )
  }

  const listStyle: React.CSSProperties = {
    maxHeight: '200px',
    overflowY: 'auto',
    marginBottom: '16px'
  }

  const itemStyle: React.CSSProperties = {
    background: '#f9f9f9',
    padding: '8px',
    marginBottom: '8px',
    borderRadius: '4px',
    border: '1px solid #e0e0e0'
  }

  const nameStyle: React.CSSProperties = {
    fontWeight: 'bold',
    marginBottom: '4px'
  }

  const timeStyle: React.CSSProperties = {
    fontSize: '11px',
    color: '#666',
    marginBottom: '8px'
  }

  const buttonGroupStyle: React.CSSProperties = {
    display: 'flex',
    gap: '4px'
  }

  const smallButtonStyle: React.CSSProperties = {
    padding: '4px 8px',
    fontSize: '11px',
    border: 'none',
    borderRadius: '3px',
    cursor: 'pointer'
  }

  const restoreButtonStyle: React.CSSProperties = {
    ...smallButtonStyle,
    background: '#1976d2',
    color: 'white'
  }

  const deleteButtonStyle: React.CSSProperties = {
    ...smallButtonStyle,
    background: '#d32f2f',
    color: 'white'
  }

  return (
    <div style={listStyle}>
      {sessions.map((session) => (
        <div key={session.id} style={itemStyle}>
          <div style={nameStyle}>{session.name}</div>
          <div style={timeStyle}>
            创建: {new Date(session.createdAt).toLocaleString()}
            <br />
            访问: {new Date(session.lastAccessed).toLocaleString()}
          </div>
          <div style={buttonGroupStyle}>
            <button style={restoreButtonStyle} onClick={() => onRestore(session.id)}>
              恢复
            </button>
            <button style={deleteButtonStyle} onClick={() => onDelete(session.id)}>
              删除
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}

// 按钮组件
interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'danger'
  onClick: () => void
  disabled?: boolean
  size?: 'normal' | 'small'
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  onClick,
  disabled = false,
  size = 'normal'
}) => {
  const [isHovered, setIsHovered] = useState(false)

  const baseStyle: React.CSSProperties = {
    padding: size === 'small' ? '6px 12px' : '10px 16px',
    border: 'none',
    borderRadius: '6px',
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontSize: size === 'small' ? '12px' : '14px',
    transition: 'background-color 0.2s',
    width: '100%',
    opacity: disabled ? 0.6 : 1
  }

  const variants = {
    primary: {
      background: isHovered && !disabled ? '#1565c0' : '#1976d2',
      color: 'white'
    },
    secondary: {
      background: isHovered && !disabled ? '#e0e0e0' : '#f5f5f5',
      color: '#333'
    },
    danger: {
      background: isHovered && !disabled ? '#c62828' : '#d32f2f',
      color: 'white'
    }
  }

  const buttonStyle: React.CSSProperties = { ...baseStyle, ...variants[variant] }

  return (
    <button
      style={buttonStyle}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </button>
  )
}

// 输入对话框组件
interface InputDialogProps {
  visible: boolean
  title: string
  placeholder: string
  defaultValue?: string
  onConfirm: (value: string) => void
  onCancel: () => void
}

const InputDialog: React.FC<InputDialogProps> = ({
  visible,
  title,
  placeholder,
  defaultValue = '',
  onConfirm,
  onCancel
}) => {
  const [value, setValue] = useState(defaultValue)

  useEffect(() => {
    setValue(defaultValue)
  }, [defaultValue, visible])

  if (!visible) return null

  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0,0,0,0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  }

  const dialogStyle: React.CSSProperties = {
    background: 'white',
    padding: '20px',
    borderRadius: '8px',
    width: '280px',
    boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
  }

  const titleStyle: React.CSSProperties = {
    marginBottom: '16px',
    fontWeight: 'bold'
  }

  const inputStyle: React.CSSProperties = {
    width: '100%',
    padding: '8px',
    marginBottom: '16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px'
  }

  const buttonGroupStyle: React.CSSProperties = {
    display: 'flex',
    gap: '8px'
  }

  const handleConfirm = () => {
    if (value.trim()) {
      onConfirm(value.trim())
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleConfirm()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  return (
    <div style={overlayStyle} onClick={onCancel}>
      <div style={dialogStyle} onClick={(e) => e.stopPropagation()}>
        <div style={titleStyle}>{title}</div>
        <input
          style={inputStyle}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyPress}
          autoFocus
        />
        <div style={buttonGroupStyle}>
          <Button variant="secondary" onClick={onCancel} size="small">
            取消
          </Button>
          <Button variant="primary" onClick={handleConfirm} size="small" disabled={!value.trim()}>
            确定
          </Button>
        </div>
      </div>
    </div>
  )
}

// 主应用组件
const SessionBoxPopup: React.FC = () => {
  const [domain, setDomain] = useState<string>('')
  const [sessions, setSessions] = useState<SessionInfo[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [sessionsLoading, setSessionsLoading] = useState<boolean>(false)
  const [status, setStatus] = useState<StatusState>({
    message: '',
    type: 'success',
    visible: false
  })
  const [currentTab, setCurrentTab] = useState<TabInfo | null>(null)
  const [showSaveDialog, setShowSaveDialog] = useState<boolean>(false)

  // 显示状态消息
  const showStatus = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    setStatus({ message, type, visible: true })
    setTimeout(() => {
      setStatus((prev) => ({ ...prev, visible: false }))
    }, 3000)
  }, [])

  // 获取当前标签页
  const getCurrentTab = useCallback(async (): Promise<TabInfo | null> => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      setCurrentTab(tab)
      return tab as TabInfo
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      showStatus('获取标签页信息失败', 'error')
      return null
    }
  }, [showStatus])

  // 获取域名信息
  const getDomainInfo = useCallback((tab: TabInfo) => {
    if (!tab.url) return

    try {
      const url = new URL(tab.url)
      setDomain(url.hostname)
    } catch (error) {
      console.error('解析域名失败:', error)
      setDomain('无法解析域名')
    } finally {
      setLoading(false)
    }
  }, [])

  // 发送消息到后台
  const sendBackgroundMessage = useCallback(async (message: Placeholder): Promise<Placeholder> => {
    try {
      const response = await new Promise((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          const tab = tabs[0]

          chrome.runtime.sendMessage(
            {
              ...message,
              tab: {
                id: tab.id,
                url: tab.url
              }
            },
            resolve
          )
        })
      })

      if (chrome.runtime.lastError) {
        throw new Error(chrome.runtime.lastError.message)
      }

      return response
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }, [])

  // 发送消息到内容脚本
  const sendContentMessage = useCallback(
    async (message: Placeholder): Promise<Placeholder> => {
      if (!currentTab?.id) {
        throw new Error('未找到当前标签页')
      }

      try {
        const response = await new Promise((resolve) => {
          chrome.tabs.sendMessage(currentTab.id!, message, resolve)
        })

        if (chrome.runtime.lastError) {
          throw new Error(chrome.runtime.lastError.message)
        }

        return response
      } catch (error) {
        console.error('发送内容脚本消息失败:', error)
        throw error
      }
    },
    [currentTab]
  )

  // 加载会话列表
  const loadSessions = useCallback(async () => {
    setSessionsLoading(true)
    try {
      const response = await sendBackgroundMessage({ action: 'listSessions' })
      if (response.success && response.sessions) {
        setSessions(response.sessions)
      } else {
        setSessions([])
        if (response.error) {
          showStatus(response.error, 'error')
        }
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
      showStatus('加载会话列表失败', 'error')
      setSessions([])
    } finally {
      setSessionsLoading(false)
    }
  }, [sendBackgroundMessage, showStatus])

  // 保存当前会话
  const handleSaveSession = useCallback(
    async (sessionName: string) => {
      try {
        setShowSaveDialog(false)
        showStatus('正在保存会话...', 'info')

        // 获取localStorage数据
        const localStorageResponse = await sendContentMessage({ action: 'getLocalStorage' })
        if (!localStorageResponse.success) {
          throw new Error('获取localStorage数据失败')
        }

        // 保存会话
        const response = await sendBackgroundMessage({
          action: 'saveSession',
          sessionName,
          localStorageData: localStorageResponse.localStorageData
        })

        if (response.success) {
          showStatus('会话保存成功', 'success')
          await loadSessions() // 重新加载会话列表
        } else {
          throw new Error(response.error || '保存会话失败')
        }
      } catch (error) {
        console.error('保存会话失败:', error)
        showStatus((error as Error).message, 'error')
      }
    },
    [sendBackgroundMessage, sendContentMessage, showStatus, loadSessions]
  )

  // 恢复会话
  const handleRestoreSession = useCallback(
    async (sessionId: string) => {
      try {
        showStatus('正在恢复会话...', 'info')

        const response = await sendBackgroundMessage({
          action: 'restoreSession',
          sessionId
        })

        if (response.success) {
          showStatus('会话恢复成功，页面即将刷新', 'success')
          setTimeout(() => {
            if (currentTab?.id) {
              void chrome.tabs.reload(currentTab.id)
            }
            window.close()
          }, 1000)
        } else {
          throw new Error(response.error || '恢复会话失败')
        }
      } catch (error) {
        console.error('恢复会话失败:', error)
        showStatus((error as Error).message, 'error')
      }
    },
    [sendBackgroundMessage, showStatus, currentTab]
  )

  // 删除会话
  const handleDeleteSession = useCallback(
    async (sessionId: string) => {
      if (!confirm('确定要删除这个会话吗？此操作不可恢复。')) {
        return
      }

      try {
        const response = await sendBackgroundMessage({
          action: 'deleteSession',
          sessionId
        })

        if (response.success) {
          showStatus('会话删除成功', 'success')
          await loadSessions() // 重新加载会话列表
        } else {
          throw new Error(response.error || '删除会话失败')
        }
      } catch (error) {
        console.error('删除会话失败:', error)
        showStatus((error as Error).message, 'error')
      }
    },
    [sendBackgroundMessage, showStatus, loadSessions]
  )

  // 清除当前域名的所有cookies和localStorage
  const handleClearAll = useCallback(async () => {
    if (!confirm('确定要清除当前域名的所有cookies和localStorage吗？此操作不可恢复。')) {
      return
    }

    try {
      showStatus('正在清除数据...', 'info')

      // 清除localStorage
      await sendContentMessage({ action: 'clearLocalStorage' })

      // 清除cookies (通过重新加载页面后cookies会被浏览器清理，或者可以通过后台脚本清理)
      showStatus('数据清除成功，页面即将刷新', 'success')
      setTimeout(() => {
        if (currentTab?.id) {
          void chrome.tabs.reload(currentTab.id)
        }
        window.close()
      }, 1000)
    } catch (error) {
      console.error('清除数据失败:', error)
      showStatus('清除数据失败', 'error')
    }
  }, [sendContentMessage, showStatus, currentTab])

  // 初始化
  useEffect(() => {
    const init = async () => {
      const tab = await getCurrentTab()
      if (tab) {
        getDomainInfo(tab)
        await loadSessions()
      }
    }

    void init()
  }, [getCurrentTab, getDomainInfo, loadSessions])

  // 样式
  const containerStyle: React.CSSProperties = {
    width: '360px',
    padding: '16px',
    fontSize: '14px'
  }

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '16px',
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: '16px'
  }

  const sectionStyle: React.CSSProperties = {
    marginBottom: '16px'
  }

  const sectionTitleStyle: React.CSSProperties = {
    fontWeight: 'bold',
    marginBottom: '8px',
    color: '#333'
  }

  const buttonsStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  }

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>🔒 会话数据管理</div>

      <DomainInfo domain={domain} loading={loading} />

      <div style={sectionStyle}>
        <div style={sectionTitleStyle}>保存的会话</div>
        <SessionList
          sessions={sessions}
          onRestore={handleRestoreSession}
          onDelete={handleDeleteSession}
          loading={sessionsLoading}
        />
      </div>

      <div style={sectionStyle}>
        <div style={sectionTitleStyle}>操作</div>
        <div style={buttonsStyle}>
          <Button
            variant="primary"
            onClick={() => setShowSaveDialog(true)}
            disabled={loading || !domain}
          >
            💾 保存当前会话
          </Button>

          <Button variant="danger" onClick={handleClearAll} disabled={loading || !domain}>
            🗑️ 清除所有数据
          </Button>
        </div>
      </div>

      <StatusMessage message={status.message} type={status.type} visible={status.visible} />

      <InputDialog
        visible={showSaveDialog}
        title="保存会话"
        placeholder="请输入会话名称"
        defaultValue={`会话_${new Date().toLocaleString()}`}
        onConfirm={handleSaveSession}
        onCancel={() => setShowSaveDialog(false)}
      />
    </div>
  )
}

// 渲染应用
const container = document.getElementById('root')
if (container) {
  const root = createRoot(container)
  root.render(<SessionBoxPopup />)
}
