// content.ts - Content Script在页面中运行
console.log('Simple Session Box Content Script 加载')

// 消息类型定义
interface LocalStorageData {
  [key: string]: string
}

interface ContentMessage {
  action: 'getLocalStorage' | 'restoreLocalStorage' | 'clearLocalStorage'
  localStorageData?: LocalStorageData
}

interface ContentResponse {
  success: boolean
  localStorageData?: LocalStorageData
  error?: string
}

// Content Script管理类
class ContentScriptManager {
  private domain: string

  constructor() {
    this.domain = window.location.hostname
    this.initialize()
  }

  private initialize(): void {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.init.bind(this))
    } else {
      this.init()
    }

    this.setupMessageListener()
  }

  private init(): void {
    console.log('Content Script 初始化完成，域名:', this.domain)
  }

  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener(
      (
        request: ContentMessage,
        sender: chrome.runtime.MessageSender,
        sendResponse: (response: ContentResponse) => void
      ) => {
        return this.handleMessage(request, sender, sendResponse)
      }
    )
  }

  private handleMessage(
    request: ContentMessage,
    _sender: chrome.runtime.MessageSender,
    sendResponse: (response: ContentResponse) => void
  ): boolean {
    console.log('收到来自background的消息:', request)

    switch (request.action) {
      case 'getLocalStorage':
        window.postMessage(
          {
            type: 'FROM_EXTENSION',
            action: 'sendData',
            data: { message: 'Hello from extension!' }
          },
          '*'
        )
        this.handleGetLocalStorage(sendResponse)
        break

      case 'restoreLocalStorage':
        this.handleRestoreLocalStorage(request, sendResponse)
        break

      case 'clearLocalStorage':
        this.handleClearLocalStorage(sendResponse)
        break

      default:
        console.warn('未知的消息action:', request.action)
        sendResponse({ success: false, error: 'Unknown action' })
    }

    return true // 保持消息通道开放
  }

  private handleGetLocalStorage(sendResponse: (response: ContentResponse) => void): void {
    try {
      const localStorageData: LocalStorageData = {}

      // 遍历localStorage获取所有数据
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key) {
          const value = localStorage.getItem(key)
          if (value !== null) {
            localStorageData[key] = value
          }
        }
      }

      console.log(`获取localStorage数据，共 ${Object.keys(localStorageData).length} 项`)
      sendResponse({ success: true, localStorageData })
    } catch (error) {
      console.error('获取localStorage失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  private handleRestoreLocalStorage(
    request: ContentMessage,
    sendResponse: (response: ContentResponse) => void
  ): void {
    try {
      if (!request.localStorageData) {
        throw new Error('缺少localStorage数据')
      }

      // 清空当前localStorage
      localStorage.clear()

      // 恢复localStorage数据
      for (const [key, value] of Object.entries(request.localStorageData)) {
        localStorage.setItem(key, value)
      }

      console.log(`恢复localStorage数据，共 ${Object.keys(request.localStorageData).length} 项`)
      sendResponse({ success: true })

      // 刷新页面以应用新的localStorage数据
      setTimeout(() => {
        window.location.reload()
      }, 100)
    } catch (error) {
      console.error('恢复localStorage失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  private handleClearLocalStorage(sendResponse: (response: ContentResponse) => void): void {
    try {
      const itemCount = localStorage.length
      localStorage.clear()

      console.log(`清空localStorage，共删除 ${itemCount} 项`)
      sendResponse({ success: true })
    } catch (error) {
      console.error('清空localStorage失败:', error)
      sendResponse({ success: false, error: (error as Error).message })
    }
  }

  // 公共方法：获取当前域名
  public getDomain(): string {
    return this.domain
  }

  // 公共方法：获取localStorage数据
  public async getLocalStorageData(): Promise<LocalStorageData> {
    return new Promise((resolve, reject) => {
      this.handleGetLocalStorage((response) => {
        if (response.success && response.localStorageData) {
          resolve(response.localStorageData)
        } else {
          reject(new Error(response.error || 'Failed to get localStorage'))
        }
      })
    })
  }
}

// 创建并启动Content Script管理器
const contentManager = new ContentScriptManager()

window.__CONTENT_MANAGER__ = contentManager

// 监听页面卸载事件进行清理
window.addEventListener('beforeunload', () => {
  console.log('页面即将卸载')
})

// 页面可见性变化监听（可选）
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    console.log('页面变为可见')
  }
})

export default contentManager
