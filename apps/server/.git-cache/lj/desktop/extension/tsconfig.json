{"compilerOptions": {"module": "esnext", "target": "esnext", "sourceMap": false, "experimentalDecorators": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "Node", "skipLibCheck": true, "strict": true, "isolatedModules": true, "baseUrl": ".", "jsx": "react-jsx", "types": ["vite/client", "@types/chrome"], "lib": ["ESNext", "dom", "dom.iterable"]}, "include": ["src"]}