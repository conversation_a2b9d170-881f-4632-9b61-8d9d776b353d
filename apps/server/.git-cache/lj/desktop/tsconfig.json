{"compilerOptions": {"module": "esnext", "target": "esnext", "sourceMap": false, "experimentalDecorators": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "Node", "skipLibCheck": true, "strict": true, "isolatedModules": true, "baseUrl": "./packages", "jsx": "react-jsx", "types": ["vite/client"], "paths": {"@renderer/*": ["renderer/src/*"], "electron-main/*": ["main/src/*"], "electron-preload/*": ["preload/src/*"], "electron-global/*": ["global/*"]}, "lib": ["ESNext", "dom", "dom.iterable"]}, "include": ["packages", "types.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}