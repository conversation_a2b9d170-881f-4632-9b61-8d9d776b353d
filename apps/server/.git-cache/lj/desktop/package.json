{"name": "account-open", "productName": "账号登陆器", "version": "1.0.5", "engines": {"node": ">=22", "pnpm": ">=8"}, "author": "长沙草儿绽放科技有限公司", "description": "App", "private": true, "main": "packages/main/dist/index.cjs", "scripts": {"postinstall": "electron-builder install-app-deps", "gen-cert": "node ./scripts/genKey.mjs", "prepare": "husky install", "build": "pnpm run build:main && pnpm run build:preload && pnpm run build:renderer", "build:main": "cd ./packages/main && node build.js", "build:preload": "cd ./packages/preload && node build.js", "build:renderer": "cd ./packages/renderer && vite build", "build-local:app": "electron-builder build --config .electron-builder.config.local.js", "compile-local": "cross-env NODE_ENV=staging pnpm run build && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false pnpm run build-local:app", "compile": "cross-env NODE_ENV=production pnpm run build && cross-env CSC_IDENTITY_AUTO_DISCOVERY=false pnpm run build:app", "build-app:production:win-ia32": "cross-env NODE_ENV=production pnpm run build && electron-builder --config .electron-builder.config.production.js --win --ia32", "build-app:production:win-x64": "cross-env NODE_ENV=production pnpm run build && electron-builder --config .electron-builder.config.production.js --win --x64", "build-app:production:mac-arm64": "cross-env NODE_ENV=production pnpm run build && electron-builder --config .electron-builder.config.production.js --mac --arm64", "build-app:production:mac-x64": "cross-env NODE_ENV=production pnpm run build && electron-builder --config .electron-builder.config.production.js --mac --x64", "build-app:staging:win-ia32": "cross-env NODE_ENV=staging pnpm run build && electron-builder --config .electron-builder.config.staging.js --win --ia32", "build-app:staging:win-x64": "cross-env NODE_ENV=staging pnpm run build && electron-builder --config .electron-builder.config.staging.js --win --x64", "build-app:staging:mac-arm64": "cross-env NODE_ENV=staging pnpm run build && electron-builder --config .electron-builder.config.staging.js --mac --arm64", "build-app:staging:mac-x64": "cross-env NODE_ENV=staging pnpm run build && electron-builder --config .electron-builder.config.staging.js --mac --x64", "watch": "node scripts/watch.mjs", "lint": "eslint ./packages --ext js,mjs,cjs,ts,tsx,mts,cts", "typecheck": "tsc --noEmit -p tsconfig.json", "format": "prettier --write \"packages/**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts,json}\"", "update-deps": "pnpm update --interactive --latest", "update-electron-vendors": "ELECTRON_RUN_AS_NODE=1 electron scripts/update-electron-vendors.mjs"}, "devDependencies": {"@electron/notarize": "2.3.2", "@tailwindcss/vite": "^4.1.11", "@types/fs-extra": "^11.0.4", "@types/lodash.debounce": "^4.0.9", "@types/lodash.throttle": "^4.1.9", "@types/mime": "^3.0.0", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/tinycolor2": "^1.4.6", "@vercel/ncc": "^0.38.1", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^4.7.0", "@yixiaoer/context-menu": "workspace:*", "@yixiaoer/platform-service": "^2.12.19", "chalk": "^5.3.0", "cross-env": "7.0.3", "electron": "37.2.4", "electron-builder": "^24.13.3", "electron-global": "link:./packages/global", "electron-store": "^8.2.0", "electron-updater": "^6.6.2", "eslint": "9.31.0", "eslint-config-prettier": "^10.1.8", "husky": "^9.1.7", "log4js": "^6.9.1", "mime": "3.0.0", "node-downloader-helper": "^2.1.9", "node-forge": "^1.3.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "rimraf": "^6.0.1", "tailwindcss": "^4.1.11", "typescript": "5.8.3", "typescript-eslint": "^8.38.0", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "axios": "^1.10.0", "clsx": "^2.1.1", "dayjs": "^1.11.12", "deepmerge": "^4.3.1", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lucide-react": "^0.517.0", "nanoid": "^3.3.11", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1"}, "pnpm": {"allowedDeprecatedVersions": {"boolean": "*", "glob": "*", "inflight": "*", "rimraf": "*", "@npmcli/move-file": "*"}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "electron", "electron-winstaller", "esbuild", "tos-crc64-js"]}}