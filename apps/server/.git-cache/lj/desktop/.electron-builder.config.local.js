const rimraf = require('rimraf')

rimraf.sync('./dist')

const publishUrl = `https://lite-download.yixiaoer.cn/latest-open`

let channel_arch = process.arch
// 如果是windows系统，且指定了ia32，则使用ia32架构
if (process.argv.some((x) => x === '--win')) {
  if (process.argv.some((x) => x === '--ia32')) {
    channel_arch = 'ia32'
  }
} else if (process.argv.some((x) => x === '--mac')) {
  if (process.argv.some((x) => x === '--arm64')) {
    channel_arch = 'arm64'
  }
}
/**
 * @type {import('electron-builder').Configuration}
 * @see https://www.electron.build/configuration/configuration
 */
const config = {
  appId: 'chat.buby.desktop.com',
  directories: {
    output: 'dist'
  },
  files: ['packages/**/dist/**', '!node_modules/**'],
  productName: 'yixiaoer',
  artifactName: 'yixiaoer-999.0.0.${ext}',
  asar: true,

  publish: [
    {
      provider: 'generic',
      channel: `${process.platform}-${channel_arch}`,
      url: publishUrl
    }
  ],
  mac: {
    target: [
      { target: 'dmg', arch: 'arm64' },
      { target: 'zip', arch: 'arm64' }
    ],
    extendInfo: {
      NSMicrophoneUsageDescription: '请允许本程序访问您的麦克风',
      NSCameraUsageDescription: '请允许本程序访问您的摄像头'
    },
    electronLanguages: ['zh-CN', 'en-US']
  },
  nsis: {
    deleteAppDataOnUninstall: true,
    oneClick: false,
    perMachine: false,
    allowElevation: false,
    allowToChangeInstallationDirectory: true
  },
  win: {
    requestedExecutionLevel: 'requireAdministrator',

    target: [
      { target: 'nsis', arch: 'x64' },
      { target: 'zip', arch: 'x64' }
    ],
    electronLanguages: ['zh-CN', 'en-US']
  }
}

module.exports = config
