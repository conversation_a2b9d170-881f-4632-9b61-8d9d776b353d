import { build, createServer } from 'vite'
import { spawn } from 'child_process'
import electronPath from 'electron'
import chalk from 'chalk'

const mode = process.env.MODE || 'dev'

const logLevel = 'silent'

const AppInfo = chalk.blue('Electron:')

/**
 * 为 `main` 包设置观察者
 * 更改它完全重新启动电子应用程序。
 * 监听 renderer server 实例
 * 需要从以下位置设置 `VITE_DEV_SERVER_URL` 环境变量
 */
function setupMainPackageWatcher({ resolvedUrls }) {
  process.env.VITE_DEV_SERVER_URL = resolvedUrls?.local[0]

  let electronApp = null

  let sigintListener

  return build({
    mode,
    logLevel,
    configFile: 'packages/main/vite.config.ts',
    build: {
      /**
       * 设置为 {} 以启用观察器
       * @see https://vitejs.dev/config/build-options.html#build-watch
       */
      watch: {}
    },
    plugins: [
      {
        name: 'reload-app-on-main-package-change',
        watchChange(id, change) {
          console.log(`${AppInfo} ${chalk.yellow(change.event)} ${id.replace(process.cwd(), '')}`)
        },
        writeBundle() {
          /** 如果进程已经存在则 kill */
          if (electronApp !== null) {
            electronApp.stdout.removeAllListeners('data')
            electronApp.stderr.removeAllListeners('data')
            electronApp.removeListener('exit', process.exit)

            process.kill(electronApp.pid)
            electronApp = null
          }

          if (sigintListener) {
            process.removeListener('SIGINT', sigintListener)
          }

          electronApp = spawn(String(electronPath), ['.'])

          electronApp.stdout.on('data', (data) => {
            const info = `${data}`.trim()
            if (info.length) {
              console.log(`${chalk.cyan('Info')}: ${info}`.trim())
            }
          })

          electronApp.stderr.on('data', (data) => {
            const message = data.toString()
            // Check if the error might be related to preload
            if (message.includes('preload') || message.includes('Uncaught Exception')) {
              console.log(`${chalk.red('Preload Error')}: ${data}`.trim())
              return
            }

            /* electron 自带错误不影响开发 */
            if (
              !/CertVerifyProcBuiltin/.test(message) &&
              !/"code":-32601/.test(message) &&
              !/devtools_app\.html/.test(message) &&
              !/subclass]/.test(message) &&
              !/IMKCFRunLoopWakeUpReliable/.test(message) &&
              !/version 2/.test(message) &&
              !/CoreText note: Client requested name/.test(message) &&
              !/Class WebSwapCGLLayer is implemented in both/.test(message) &&
              !/Electron sandbox_bundle\.js script failed to run/.test(message) &&
              !/source: node:electron\/js2c\/sandbox_bundle/.test(message) &&
              !/console\.assert/.test(message) &&
              !/formatjs/.test(message) &&
              !/NSCameraUseContinuityCameraDeviceType/.test(message) &&
              !/NSApplicationDelegate\.applicationSupportsSecureRestorableState/.test(message)
            ) {
              console.log(`${chalk.red('Main Error')}: ${data}`.trim())
            }
          })

          sigintListener = () => {
            electronApp && electronApp.pid && process.kill(electronApp.pid)
          }
          // control + C
          process.on('SIGINT', sigintListener)

          // 保留备用
          // electronApp.stdout.pipe(process.stdout)

          /** 当应用程序退出时停止监视脚本 */
          electronApp.addListener('exit', process.exit)
        },
        buildEnd(error) {
          if (error) {
            console.log(`${chalk.red('Main Build Error:')} ${error}`)
          }
        }
      }
    ]
  })
}

/**
 * 设置`预加载`包的观察程序
 * 在文件更改它重新加载网页。
 * 监听 renderer server 实例
 * 需要访问页面的 Web 套接字。通过将"完全重新加载"命令发送到套接字，它会重新加载网页
 */
function setupPreloadPackageWatcher({ ws }) {
  return build({
    mode,
    logLevel,
    configFile: 'packages/preload/vite.config.ts',
    build: {
      /**
       * Set to {} to enable rollup watcher
       * @see https://vitejs.dev/config/build-options.html#build-watch
       */
      watch: {}
    },
    plugins: [
      {
        name: 'reload-page-on-preload-package-change',
        watchChange(id, change) {
          console.log(`${AppInfo} ${chalk.yellow(change.event)} ${id.replace(process.cwd(), '')}`)
        },
        writeBundle() {
          ws.send({
            type: 'full-reload'
          })
        },
        buildEnd(error) {
          if (error) {
            console.log(`${chalk.red('Preload Build Error:')} ${error}`)
          }
        }
      }
    ]
  })
}

/**
 * 渲染器的开发服务器
 * 因为 {@link setupMainPackageWatcher} and {@link setupPreloadPackageWatcher}
 * 取决于开发服务器的属性
 */
async function runServer() {
  const rendererWatchServer = await createServer({
    mode,
    logLevel,
    configFile: 'packages/renderer/vite.config.mts'
  })

  console.log(`${AppInfo} ${chalk.green(`Start Renderer `)}`)
  await rendererWatchServer.listen()

  try {
    console.log(`${AppInfo} ${chalk.green('Start Preload')}`)
    await setupPreloadPackageWatcher(rendererWatchServer)
  } catch (error) {
    console.log(`${AppInfo} ${chalk.red(`Preload setup error: ${error}`)}`)
  }
  try {
    console.log(`${AppInfo} ${chalk.green('Start Main')}`)
    await setupMainPackageWatcher(rendererWatchServer)
  } catch (error) {
    console.log(`${AppInfo} ${chalk.red(`Main setup error: ${error}`)}`)
  }
}

// Clear console and run server
process.stdout.write(process.platform === 'win32' ? '\x1B[2J\x1B[0f' : '\x1B[2J\x1B[3J\x1B[H')
void runServer()
