import fs from 'fs-extra'
import path from 'path'
import crypto from 'crypto'
import forge from 'node-forge'

function createSelfSignedCertWithForge() {
  const pki = forge.pki

  console.log('🔑 生成 RSA 密钥对...')
  // 生成 2048 位 RSA 密钥对
  const keys = pki.rsa.generateKeyPair(2048)

  console.log('📋 创建证书...')
  // 创建证书
  const cert = pki.createCertificate()

  // 设置公钥
  cert.publicKey = keys.publicKey

  // 设置序列号（随机生成）
  cert.serialNumber = Math.floor(Math.random() * 1000000).toString()

  // 设置有效期
  cert.validity.notBefore = new Date()
  cert.validity.notAfter = new Date()
  cert.validity.notAfter.setFullYear(cert.validity.notBefore.getFullYear() + 1) // 1年有效期

  // 设置证书主题信息
  const attrs = [
    {
      name: 'countryName',
      value: 'CN'
    },
    {
      name: 'stateOrProvinceName',
      value: 'Local'
    },
    {
      name: 'localityName',
      value: 'Local'
    },
    {
      name: 'organizationName',
      value: 'Electron Local Server'
    },
    {
      name: 'organizationalUnitName',
      value: 'Development'
    },
    {
      name: 'commonName',
      value: 'localhost'
    }
  ]

  // 设置主题和颁发者（自签名）
  cert.setSubject(attrs)
  cert.setIssuer(attrs)

  console.log('🔧 添加证书扩展...')
  // 添加证书扩展
  cert.setExtensions([
    {
      name: 'basicConstraints',
      cA: true,
      critical: true
    },
    {
      name: 'keyUsage',
      keyCertSign: true,
      digitalSignature: true,
      nonRepudiation: true,
      keyEncipherment: true,
      dataEncipherment: true,
      critical: true
    },
    {
      name: 'extKeyUsage',
      serverAuth: true,
      clientAuth: true
    },
    {
      name: 'nsCertType',
      server: true,
      client: true
    },
    {
      name: 'subjectAltName',
      altNames: [
        {
          type: 2, // DNS name
          value: 'localhost'
        },
        {
          type: 2, // DNS name
          value: '*.localhost'
        },
        {
          type: 7, // IP address
          ip: '127.0.0.1'
        },
        {
          type: 7, // IP address
          ip: '::1'
        }
      ]
    },
    {
      name: 'subjectKeyIdentifier'
    },
    {
      name: 'authorityKeyIdentifier'
    }
  ])

  console.log('✍️ 签名证书...')
  // 使用私钥自签名（使用 SHA-256）
  cert.sign(keys.privateKey, forge.md.sha256.create())

  // 转换为 PEM 格式
  const privateKeyPem = pki.privateKeyToPem(keys.privateKey)
  const certificatePem = pki.certificateToPem(cert)

  console.log('✅ 证书创建成功!')
  console.log('📊 证书信息:')
  console.log(`   序列号: ${cert.serialNumber}`)
  console.log(
    `   有效期: ${cert.validity.notBefore.toISOString()} - ${cert.validity.notAfter.toISOString()}`
  )
  console.log(`   主题: ${cert.subject.getField('CN').value}`)

  return {
    privateKey: keys.privateKey,
    publicKey: keys.publicKey,
    certificate: cert,
    privateKeyPem,
    certificatePem
  }
}

const mainPath = path.join(process.cwd(), 'packages', 'main', 'assets')
const keyFilePath = path.join(mainPath, 'server.key')
const certFilePath = path.join(mainPath, 'server.crt')

console.log('🔐 开始生成自签名证书...')

const result = createSelfSignedCertWithForge()

// 保存文件
fs.writeFileSync(keyFilePath, result.privateKeyPem)
fs.writeFileSync(certFilePath, result.certificatePem)

console.log('✅ 证书生成完成!')
console.log('📁 私钥文件:', keyFilePath)
console.log('📁 证书文件:', certFilePath)
