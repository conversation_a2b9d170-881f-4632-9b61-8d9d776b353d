/**
 * 任意对象类型
 */
type AnyObject = Record<PropertyKey, any>

/**
 * 占位使用
 */
type Placeholder = any

const __APP_VERSION__: string
const __IS_PROD__: boolean

/**
 * 判断两个类型是否完全相等
 */
type IfEqual<X, Y> =
  (<T>() => T extends X ? 1 : 2) extends <T>() => T extends Y ? 1 : 2 ? true : false

declare module '@emoji-mart/react' {
  export default (props: Record<PropertyKey, any> & { data: import('emoji-mart').Data }) =>
    JSX.Element
}

declare module 'sharp-local' {
  export default (await import('sharp')).default
}

declare module '@emoji-mart/data' {
  export default import('emoji-mart').Data
}
