name: Production Build

on:
  push:
    tags:
      - 'v*.*.*-production-open'
      - 'v*.*.*-rc.*'

env:
  SECURE_FILES_DOWNLOAD_PATH: 'build'
  DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/download-secure-files-windows-386.exe'
  DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/download-secure-files-windows-amd64.exe'
  DOWNLOAD_SECURE_FILES_INSTALL_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/installer'

jobs:
  build-windows-x64:
    runs-on: win32-x64
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download secure files
        run: |
          Invoke-WebRequest -Uri $env:DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL -OutFile "download-secure-files-windows.exe"
          Start-Process -Wait "download-secure-files-windows.exe"
        shell: powershell

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=win32 --config.architecture=x64
          pnpm run build-app:production:win-x64
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
        shell: powershell

  build-windows-ia32:
    runs-on: win32-x64
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download secure files
        run: |
          Invoke-WebRequest -Uri $env:DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL -OutFile "download-secure-files-windows.exe"
          Start-Process -Wait "download-secure-files-windows.exe"
        shell: powershell

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=win32 --config.architecture=ia32
          pnpm run build-app:production:win-ia32
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
        shell: powershell

  build-macos-x64:
    runs-on: darwin-x64-local
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download secure files
        run: curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=darwin --config.architecture=x64
          pnpm run build-app:production:mac-x64
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}

  build-macos-arm64:
    runs-on: darwin-x64-local
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download secure files
        run: curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=darwin --config.architecture=arm64
          pnpm run build-app:production:mac-arm64
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
