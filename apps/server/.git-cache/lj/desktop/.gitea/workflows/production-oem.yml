name: Production Build

on:
  push:
    tags:
      - 'v*.*.*-oem-*'
      - 'v*.*.*-rc.*'
  release:
    types: [created, published]
jobs:
  # build-windows-x64:
  #   runs-on: win32-x64
  #   environment: Production
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4

  #     - name: Download secure files
  #       run: |
  #         Invoke-WebRequest -Uri $env:DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL -OutFile "download-secure-files-windows.exe"
  #         Start-Process -Wait "download-secure-files-windows.exe"
  #       shell: powershell

  #     - name: Install dependencies and build
  #       run: |
  #         pnpm i --config.platform=win32 --config.architecture=x64
  #         pnpm run build-app:production:win-x64
  #         node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
  #       shell: powershell

  # build-windows-ia32:
  #   runs-on: win32-x64
  #   environment: Production
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4

  #     - name: Download secure files
  #       run: |
  #         Invoke-WebRequest -Uri $env:DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL -OutFile "download-secure-files-windows.exe"
  #         Start-Process -Wait "download-secure-files-windows.exe"
  #       shell: powershell

  #     - name: Install dependencies and build
  #       run: |
  #         pnpm i --config.platform=win32 --config.architecture=ia32
  #         pnpm run build-app:production:win-ia32
  #         node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
  #       shell: powershell

  build-macos-x64:
    runs-on: macos-latest
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=darwin --config.architecture=x64
          pnpm run build-app:production:mac-x64
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}

  build-macos-arm64:
    runs-on: macos-latest
    environment: Production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies and build
        run: |
          pnpm i --config.platform=darwin --config.architecture=arm64
          pnpm run build-app:production:mac-arm64
          node ./build/publish.js prod ${{ secrets.CI_TOS_ACCESS_KEY_ID }} ${{ secrets.CI_TOS_ACCESS_KEY_SECRET }} ${{ github.ref_name }}
