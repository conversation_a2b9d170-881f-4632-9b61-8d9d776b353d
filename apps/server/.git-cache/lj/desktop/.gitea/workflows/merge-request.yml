name: Merge Request Build

on:
  pull_request:
    branches: [main, develop]

jobs:
  build-check:
    runs-on: ubuntu-latest
    container:
      image: node:16
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        run: |
          which node
          node -v

      - name: Install dependencies and build
        run: |
          pnpm cache clean --force
          pnpm install --force
          pnpm run build
