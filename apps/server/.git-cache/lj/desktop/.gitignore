node_modules
.DS_Store
dist
*.local
thumbs.db

pnpm-lock.yaml
.eslintcache

# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# <PERSON>rad<PERSON> and <PERSON><PERSON> with auto-import
# When using <PERSON>rad<PERSON> or <PERSON><PERSON> with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
 .idea/artifacts
 .idea/compiler.xml
 .idea/jarRepositories.xml
 .idea/modules.xml
 .idea/*.iml
 .idea/modules
 *.iml
 *.ipr

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# Editor-based Rest Client
.idea/httpRequests
/.idea/csv-plugin.xml


.VSCodeCounter

/extensions


.cache



.idea
