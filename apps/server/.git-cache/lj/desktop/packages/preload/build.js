const path = require('path')
const fs = require('fs-extra')
const rimraf = require('rimraf')
const ncc = require('@vercel/ncc')

rimraf.sync(path.join(__dirname, 'dist'))

const cachePath = path.join(__dirname, '.cache')

ncc(path.join(__dirname, 'src', 'index.ts'), {
  externals: ['electron'],
  filterAssetBase: process.cwd(),
  minify: true,
  cache: cachePath,
  sourceMap: false,
  assetBuilds: false
}).then(({ code, assets }) => {
  const outPath = path.join(__dirname, 'dist', 'index.cjs')

  fs.outputFileSync(
    outPath,
    code
      .replace(/new Buffer\(/g, 'Buffer.from(')
      .replace(/import\.meta\.env\.DEV/g, 'false')
      .replace(/import\.meta\.env\.VITE_DEV_SERVER_URL/g, '""')
  )
  if (assets) {
    Object.keys(assets).forEach((fileName) => {
      fs.outputFileSync(path.join(__dirname, 'dist', fileName), assets[fileName].source)
    })
  }
})
