import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { InstructEnum } from 'electron-global/enum'

export function getLogger(type: string) {
  return {
    error(...args: Placeholder[]) {
      void ipcRenderer.invoke(`${InstructEnum.Logger}`, {
        type,
        level: 'error',
        texts: args
      })
    },
    warn(...args: Placeholder[]) {
      void ipcRenderer.invoke(`${InstructEnum.Logger}`, {
        type,
        level: 'warn',
        texts: args
      })
    },
    info(...args: Placeholder[]) {
      void ipcRenderer.invoke(`${InstructEnum.Logger}`, {
        type,
        level: 'info',
        texts: args
      })
    },
    debug(...args: Placeholder[]) {
      void ipcRenderer.invoke(`${InstructEnum.Logger}`, {
        type,
        level: 'debug',
        texts: args
      })
    }
  }
}
