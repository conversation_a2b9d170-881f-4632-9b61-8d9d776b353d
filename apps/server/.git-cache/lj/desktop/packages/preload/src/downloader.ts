import { DownloaderHelper } from 'node-downloader-helper'
import { appCachePath } from 'electron-global/appCachePath'
import { FileProtocol } from 'electron-global/fileProtocol'
import fs from 'fs-extra'
import path from 'path'

export interface DownloaderCallbacks {
  url: string
  fileName?: string
  onProgress?: (progress: number) => void
  onEnd?: (path: string) => void
  onError?: () => void
}

type DownloaderPath = Omit<DownloaderCallbacks, 'onProgress' | 'onError' | 'onEnd'>

/**
 * 获取文件path info
 * @param param0
 * @returns
 */
export function getFilePathInfo({ url }: DownloaderPath) {
  const basename = path.basename(url)
  const dirPath = path.join(appCachePath)
  return { basename, dirPath }
}

/**
 * 创建下载器
 * @param param0
 * @returns
 */
export function createDownloader({
  url,
  fileName,
  onProgress = () => {},
  onEnd = () => {},
  onError = () => {}
}: DownloaderCallbacks) {
  const { basename, dirPath } = getFilePathInfo({ url })
  const currentFileName = fileName ?? basename
  const currentFilePath = path.join(dirPath, currentFileName)

  if (!fs.existsSync(dirPath)) {
    fs.ensureDirSync(dirPath)
  }

  const dl = new DownloaderHelper(url, dirPath, {
    fileName: currentFileName,
    progressThrottle: 200,
    override: {
      skip: true,
      skipSmaller: false
    }
  })

  if (onProgress) {
    let timer: NodeJS.Timeout

    dl.on('progress', ({ progress }) => {
      if (progress < 100) {
        onProgress(progress)
      } else {
        clearTimeout(timer)
        timer = setTimeout(() => {
          onProgress(progress)
          clearTimeout(timer)
        }, 500)
        onProgress(99.9)
      }
    })
  }

  dl.on('error', onError)
  dl.on('end', () => {
    onEnd && onEnd(`${FileProtocol}${currentFilePath}`)
  })

  return {
    onStart() {
      onProgress(1)
      dl.start().catch((error: Error) => {
        if (error.message && error.message.includes('404')) {
          fs.outputFileSync(currentFilePath, '')
        }
        onEnd && onEnd(url)
        // logger.debug('下载失败:', url)
      })
    },
    onPause() {
      void dl.pause()
    },
    onStop() {
      void dl.stop()
    },
    onResume() {
      void dl.resume()
    }
  }
}
