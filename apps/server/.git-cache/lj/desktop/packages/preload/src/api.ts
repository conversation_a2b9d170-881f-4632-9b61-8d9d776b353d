import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { IpcChannelEnum } from 'electron-global/enum'

export class Api {
  send = (...args: Parameters<typeof ipcRenderer.send>) => {
    ipcRenderer.send(...args)
  }
  on = (...args: Parameters<typeof ipcRenderer.on>) => {
    ipcRenderer.on(...args)
  }
  invoke = (...args: Parameters<typeof ipcRenderer.invoke>) => {
    return ipcRenderer.invoke(...args)
  }
  removeListener = (...args: Parameters<typeof ipcRenderer.removeListener>) => {
    ipcRenderer.removeListener(...args)
  }
  removeAllListeners = (...args: Parameters<typeof ipcRenderer.removeAllListeners>) => {
    ipcRenderer.removeAllListeners(...args)
  }
  sendMessage = (data: { type: number; data: unknown }) => {
    return ipcRenderer.invoke(IpcChannelEnum.Public, data)
  }
}

export const api = new Api()
