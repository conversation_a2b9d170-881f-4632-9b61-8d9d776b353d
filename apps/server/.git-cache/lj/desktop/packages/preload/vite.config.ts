import { chrome } from '../../.electron-vendors.cache.json'
import { defineConfig } from 'vite'
import path from 'path'

const PackageRoot = __dirname

/**
 * @see https://vitejs.dev/config/
 */
const config = defineConfig({
  mode: process.env.MODE,
  root: PackageRoot,
  envDir: process.cwd(),
  publicDir: path.join(PackageRoot, 'assets'),
  resolve: {
    alias: {
      'electron-global': path.join(PackageRoot, '..', 'global'),
      'electron-main': path.join(PackageRoot, '..', 'main', 'src')
    }
  },
  build: {
    ssr: true,
    target: `chrome${chrome}`,
    outDir: 'dist',
    assetsDir: '.',
    minify: process.env.MODE !== 'development',
    lib: {
      entry: 'src/index.ts',
      formats: ['cjs']
    },
    rollupOptions: {
      output: {
        entryFileNames: '[name].cjs'
      }
    },
    emptyOutDir: true,
    reportCompressedSize: false
  }
})

export default config
