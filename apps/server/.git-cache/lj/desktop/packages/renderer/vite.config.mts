import { chrome } from '../../.electron-vendors.cache.json'
import tsconfigPaths from 'vite-tsconfig-paths'
import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { version } from '../../package.json'
import basicSsl from '@vitejs/plugin-basic-ssl'

import path from 'path'

const PackageRoot = __dirname

/**
 * @see https://vitejs.dev/config/
 */
const config = defineConfig({
  /**
   * base 必须为 `''` 否则打包后找不到文件路径
   */
  base: '',
  mode: process.env.MODE,
  root: PackageRoot,
  server: {
    fs: {
      strict: true
    }
  },
  define: {
    __APP_VERSION__: JSON.stringify(version),
    __IS_PROD__: `${process.env.NODE_ENV === 'production'}`
  },
  publicDir: path.join(PackageRoot, 'public'),
  build: {
    sourcemap: false,
    target: `chrome${chrome}`,
    outDir: 'dist',
    assetsDir: '.',
    rollupOptions: {
      input: {
        main: path.join(PackageRoot, 'index.html'),
        placeholder: path.join(PackageRoot, 'placeholder.html'),
        tool: path.join(PackageRoot, 'tool.html'),
        update: path.join(PackageRoot, 'update.html'),
        online: path.join(PackageRoot, 'online.html'),
        error: path.join(PackageRoot, 'error.html')
      }
    },
    emptyOutDir: true,
    reportCompressedSize: false
  },
  plugins: [
    react(),

    basicSsl({
      name: 'account-open',
      domains: ['*.account-open.com'],
      certDir: path.join(PackageRoot, '.devServer/cert')
    }),

    tailwindcss(),
    tsconfigPaths()
  ]
})

export default config
