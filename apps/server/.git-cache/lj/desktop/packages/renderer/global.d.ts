import { Instruct<PERSON>num, WindowsInstructEnum } from 'electron-main/types'
import { Ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron'
import { ThemeMapType } from 'theme'

declare global {
  interface Window {
    simpleEvent: AnyObject
    onClose?: (...args: Placeholder) => void
    app: {
      preloadUtils: typeof import('../preload/src/tools')
      preloadDatabase: typeof import('../preload/src/database')
      ipcRenderer: {
        send: Ipc<PERSON><PERSON><PERSON>['send']
        on: Ipc<PERSON><PERSON><PERSON>['on']
        invoke: Ipc<PERSON><PERSON><PERSON>['invoke']
        removeListener: Ip<PERSON><PERSON><PERSON><PERSON>['removeListener']
        removeAllListeners: Ip<PERSON><PERSON><PERSON><PERSON>['removeAllListeners']
        sendMessage<T = unknown>(args: {
          type: InstructEnum | WindowsInstructEnum
          data?: unknown
        }): Promise<T>
      }
    }
  }
}

export {}
