import { getToolPagePath } from '@renderer/common/bridge'
import { webviewManagerAPI } from '@renderer/common/bridge/webview'
import { createWindow } from '@renderer/common/windowManagement'
import { Button } from '@renderer/components/Button'
import { useEffect, useRef, useState } from 'react'

let isCreate = false

export function BrowserToolAuth({
  isUpdate,
  isLoading
}: {
  isUpdate?: boolean
  isLoading?: boolean
}) {
  const ref = useRef<HTMLButtonElement>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const toolState = localStorage.getItem('toolState')

    const init = async () => {
      const path = await getToolPagePath()

      createWindow(
        path,
        {
          state: {
            isUpdate
          },
          windowId: 'auth',
          callback(state: 0 | 1) {
            if (!state) {
              localStorage.setItem('toolState', '1')
            }
          }
        },
        {
          width: 360,
          height: 320
        }
      )
    }
    if (!isCreate && !toolState) {
      isCreate = true
      void init()
    }
  }, [])

  return (
    <div className="flex h-[44px] items-center border-b border-gray-100">
      <Button
        disabled={isLoading}
        ref={ref}
        loading={loading}
        className="ml-auto h-[28px]"
        onClick={async () => {
          setLoading(true)

          try {
            const res = await webviewManagerAPI.saveCookies()
            if (res) {
              const activeTab = await webviewManagerAPI.getActiveTab()
              if (activeTab) {
                void webviewManagerAPI.closeTab(activeTab.id)
              }
            }
          } catch (error) {
            console.error(error)
          } finally {
            setLoading(false)
          }
        }}
      >
        {isUpdate ? '更新账号授权' : '我已完成登录'}
      </Button>
    </div>
  )
}
