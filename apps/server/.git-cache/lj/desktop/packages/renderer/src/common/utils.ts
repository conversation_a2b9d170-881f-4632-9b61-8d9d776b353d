import dayjs from 'dayjs'
import type { noop } from 'electron-global/noop'
import { isWin } from 'electron-global/platform'

export function wait(time = 1000) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

/**
 * 获取指定范围随机数
 * @param start
 * @param end
 * @returns
 */
export function randNumber(start: number, end: number) {
  return Math.floor((start - end) * Math.random() + end)
}

/**
 * sync setInterval
 */
export async function setIntervalSync(callback: (stop: () => void) => Promise<void>, time: number) {
  const state = { current: false }

  const onStop = () => {
    state.current = true
  }

  while (true) {
    if (state.current) {
      break
    }

    await callback(onStop)

    await wait(time)
  }
}

/**
 * 在指定位置插入 字符 | 字符串
 * @param source
 * @param start
 * @param newString
 * @returns
 */
export function insertString(source: string, start: number, newString: string) {
  return source.slice(0, start) + newString + source.slice(start)
}

/**
 * 替换掉指定位置的 字符 | 字符串
 * @param source
 * @param index
 * @param newString
 * @returns
 */
export function replaceString(source: string, index: number, newString: string) {
  return source.substring(0, index) + newString + source.substring(index + 1)
}

/**
 * 对应 `document.insertBefore`
 * 在次 dom 后面插入新的 dom
 * @param newElement
 * @param targetElement
 */
export function insertAfter(newElement: HTMLElement | Node, targetElement: HTMLElement | Node) {
  const parentElement = targetElement.parentNode

  if (parentElement) {
    if (parentElement.lastChild === targetElement) {
      parentElement.appendChild(newElement)
    } else {
      parentElement.insertBefore(newElement, targetElement.nextSibling)
    }
  }
}

/**
 * paste 操作没有对应的好的Api代替, 所以这里使用 `execCommand` 过期的API,可以有效降低复杂度
 * 这可能会随着 `electron` 的内核更新, 而被完全废弃
 * 所以尽可能的不要使用 `execCommand`
 * @returns
 */
export const dispatcherPaste = () => document.execCommand('paste')

/**
 * 快排
 * @param source
 * @param options
 * @returns
 */
export function quickSort<T extends Placeholder[]>(
  source: T,
  options?: {
    attr?: keyof T[number]
    change?: (item: T[number]) => void
    order?: 'asc' | 'reverse'
  }
): T {
  const { attr, change, order = 'reverse' } = options ?? {}
  if (source.length <= 1) return source
  const sourceBase = source[0]
  const left = []
  const right = []
  for (let i = 1; i < source.length; i++) {
    const sourceTarge = attr ? source[i][attr] : source[i]
    const sourceBaseTarge = attr ? sourceBase[attr] : sourceBase
    if (order === 'reverse' ? sourceTarge <= sourceBaseTarge : sourceTarge >= sourceBaseTarge) {
      left.push(change ? change(source[i]) : source[i])
    } else {
      right.push(change ? change(source[i]) : source[i])
    }
  }
  return quickSort(left as T, options).concat([sourceBase], quickSort(right as T, options)) as T
}

/**
 * 格式化时间
 * @param time
 * @returns
 */
export function formatTime(time = 0) {
  const currentTime = dayjs().format('YYYY-MM-DD')
  const targetTime = dayjs(time).format('YYYY-MM-DD')

  if (currentTime === targetTime) {
    return dayjs(time).format('HH:mm')
  }

  return dayjs(time).format('YYYY/MM/DD')
}

/**
 * 等比例缩放
 * @param width
 * @param height
 * @returns
 */
export function ratio(width: number, height: number) {
  const rate = width / height

  if (rate < 0.4) {
    width = 204
    height = 510
  } else if (rate >= 0.4 && rate <= 0.5) {
    width = 204
    height = 204 / rate
  } else if (rate > 0.5 && rate < 1) {
    width = 405 * rate
    height = 405
  } else if (rate >= 1 && rate < 2) {
    height = 405 * (1 / rate)
    width = 405
  } else if (rate >= 2 && rate < 2.5) {
    height = 204
    width = 204 / (1 / rate)
  } else if (rate >= 2.5) {
    height = 204
    width = 510
  }

  height /= 1.6
  width /= 1.6

  return { width: width | 0 || 160, height: height | 0 || 160 }
}

/**
 * 格式化文件大小
 * @param filesize
 * @returns
 */
export function formatSize(filesize: number) {
  const threshold = isWin ? 1024 : 1000
  let size = ''
  if (filesize < 0.1 * threshold) {
    size = filesize.toFixed(2) + 'B'
  } else if (filesize < 0.1 * threshold * threshold) {
    size = (filesize / threshold).toFixed(2) + 'KB'
  } else if (filesize < 0.1 * threshold * threshold * threshold) {
    size = (filesize / (threshold * threshold)).toFixed(2) + 'MB'
  } else {
    size = (filesize / (threshold * threshold * threshold)).toFixed(2) + 'GB'
  }
  return size
}

/**
 * 格式化 时分秒
 * @param count
 * @returns
 */
export function formatCountDown(count = 0) {
  let h: number | string = parseInt(`${(count / 60 / 60) % 24}`, 10)
  h = h < 10 ? `0${h}` : h
  let m: number | string = parseInt(`${(count / 60) % 60}`, 10)
  m = m < 10 ? `0${m}` : m
  let s: number | string = parseInt(`${count % 60}`, 10)
  s = s < 10 ? `0${s}` : s
  return `${h}:${m}:${s}`
}

/**
 * 创建防抖函数
 * @param callback
 * @param wait
 * @returns
 */
export function createDebounce<T extends typeof noop>(callback: T, wait: number) {
  let timer: number | null
  return (...args: Parameters<T>) => {
    clearTimeout(timer!)
    timer = window.setTimeout(() => {
      callback(...args)
      clearTimeout(timer!)
      timer = null
    }, wait)
  }
}

/**
 * 创建节流函数
 * @param callback
 * @param wait
 * @returns
 */
export function createThrottle<T extends typeof noop>(callback: T, wait: number) {
  let timer: number | null
  return (...args: Parameters<T>) => {
    if (!timer) {
      timer = window.setTimeout(() => {
        callback(...args)
        clearTimeout(timer!)
        timer = null
      }, wait)
    }
  }
}

/**
 * 获取本地图片宽高
 * @param file
 * @returns
 */
export function readImage(file: File): Promise<{
  fileWidth: number
  fileHeight: number
}> {
  return new Promise((resolve) => {
    const image = new Image()
    image.src = URL.createObjectURL(file)
    image.onload = () => {
      resolve({
        fileWidth: image.naturalWidth,
        fileHeight: image.naturalHeight
      })
    }
  })
}

/**
 * 转换文件
 * @param file
 * @returns
 */
export function transfromFileBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve) => {
    const fileReader = new FileReader()
    fileReader.onload = () => {
      resolve(fileReader.result as ArrayBuffer)
    }
    fileReader.readAsArrayBuffer(file)
  })
}

/**
 * 生成搜索正则
 * @param prefix
 * @returns
 */
export function generateSearch(prefix: string) {
  return new RegExp(prefix.replace(/[`~!@#$%^&*()_|+\-=?;:'",.<>{}[\]\\/]/gi, '\\$&'), 'i')
}

/**
 * 手动触发event
 * @param param0
 */
export function manualDispatchEvent({
  dom,
  element,
  elementKey,
  event,
  value
}: {
  dom: HTMLElement | null
  element: typeof HTMLElement
  elementKey: string
  value: Placeholder
  event: string
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey)
  if (proto && proto.set && dom) {
    proto.set.call(dom, value)
    dom.dispatchEvent(new Event(event, { bubbles: true }))
  }
}

/**
 * 找交集
 * @param arr1
 * @param arr2
 * @returns
 */
export function intersection<T extends Placeholder[]>(arr1: T, arr2: T) {
  const intersection = []
  for (let i = 0; i < arr1.length; i++) {
    const item = arr1[i]
    for (let j = 0; j < arr2.length; j++) {
      if (item === arr2[j]) {
        intersection.push(item)
      }
    }
  }

  return intersection as T
}

export function hasPath(src?: string) {
  if (!src) {
    return true
  }

  return src.slice(src.length - 3) === 'svg'
}

export function uint8ArrayToFileList({
  file: uint8Array,
  fileName,
  fileType
}: {
  file: Uint8Array
  fileName: string
  fileType: string
}) {
  const blob = new Blob([uint8Array])
  const file = new File([blob], fileName, { type: fileType })
  const fileList = new DataTransfer()
  fileList.items.add(file)
  return fileList
}

export function compressImage(
  file: Blob,
  options: { quality: number }
): Promise<{ raw: Blob; width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = URL.createObjectURL(file)

    img.onload = function () {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      canvas.width = parseInt(`${img.width * options.quality}`, 10)
      canvas.height = parseInt(`${img.height * options.quality}`, 10)

      // 将图片绘制到 canvas 上
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)

      // 使用 toBlob 方法将 canvas 转换为 Blob
      canvas.toBlob(
        function (blob) {
          if (blob) {
            // 创建新的 File 对象

            // 将 File 对象传递给 resolve
            resolve({ raw: blob, width: canvas.width, height: canvas.height })
          } else {
            reject(new Error('转换为 Blob 失败'))
          }
        },
        file.type,
        options.quality
      )
    }

    img.onerror = function (_error) {
      // 如果发生错误，将错误信息传递给 reject
      reject(new Error('图片加载失败'))
    }
  })
}
