import { windowsTrafficLights } from '@renderer/common/bridge'
import { isWin } from 'electron-global/platform'
import { IpcChannelEnum } from 'electron-global/enum'
import { createContext, useContext, useState } from 'react'
import { useIpcEvent } from './hooks/useIpcEvent'

type WindowStateContextType = {
  isMaximized: boolean
  maximize: () => Promise<unknown>
  minimize: () => Promise<unknown>
  close: () => Promise<unknown>
}

const WindowStateContext = createContext<WindowStateContextType>({
  isMaximized: false,
  maximize: () => Promise.resolve(),
  minimize: () => Promise.resolve(),
  close: () => Promise.resolve()
})

type WindowStateProviderProps = {
  children: React.ReactNode
}

function WindowStateProviderDesktop(props: WindowStateProviderProps) {
  const [isMaximized, setMaximize] = useState(false)
  const { maximize, minimize, close } = windowsTrafficLights()

  if (isWin) {
    useIpcEvent<boolean>(IpcChannelEnum.WindowMaximize, (_, value) => {
      setMaximize(value)
    })
  }

  return (
    <WindowStateContext.Provider
      value={{
        isMaximized,
        maximize,
        minimize,
        close
      }}
    >
      {props.children}
    </WindowStateContext.Provider>
  )
}

export const WindowStateProvider = WindowStateProviderDesktop

export function useWindowState(): WindowStateContextType {
  const context = useContext(WindowStateContext)
  if (!context) {
    throw new Error('useWindowState must be used within a WindowStateProvider')
  }
  return context
}
