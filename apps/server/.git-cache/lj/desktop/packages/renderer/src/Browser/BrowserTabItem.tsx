import React, { useMemo } from 'react'
import { Globe, X } from 'lucide-react'
import clsx from 'clsx'
import type { DraggableProvided, DraggableStateSnapshot } from '@hello-pangea/dnd'
import type { TabInfo } from 'electron-global/webview'
import { ImageComponent } from '@renderer/components/image'

interface BrowserTabItemProps {
  isHome?: boolean
  tab: TabInfo
  isActive: boolean
  isDragging: boolean
  provided: DraggableProvided
  totalNum: number
  snapshot: DraggableStateSnapshot
  onTabClick: (tabId: string) => void
  onCloseTab: (tabId: string, event: React.MouseEvent) => void
}

export function BrowserTabItem({
  tab,
  isHome,
  isActive,
  isDragging,
  provided,
  snapshot,
  totalNum,
  onTabClick,
  onCloseTab
}: BrowserTabItemProps) {
  const style = useMemo(() => {
    const baseStyle: React.CSSProperties = {
      ...provided.draggableProps.style
    }

    if (snapshot.isDragging && baseStyle.transform) {
      const match = baseStyle.transform.match(/translate\(([-\d.]+)px, ([-\d.]+)px\)/)
      if (match) {
        baseStyle.transform = `translate(${match[1]}px, 0px)`
      }
    }

    if (!isDragging) {
      baseStyle.transition = 'width 0.2s'
    }

    return baseStyle
  }, [provided.draggableProps.style, snapshot.isDragging, isDragging])

  const faviconElement = useMemo(() => {
    return (
      <ImageComponent src={tab.favicon} alt={tab.title} className="h-4 w-4" data-tab-id={tab.id}>
        <Globe className="h-full w-full text-gray-500" />
      </ImageComponent>
    )
  }, [tab.favicon])

  return (
    <div
      data-tab-id={tab.id}
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      onClick={() => onTabClick(tab.id)}
      data-active={isActive}
      className={clsx(
        'group app-no-region tab-container relative flex h-full flex-shrink !cursor-pointer items-center overflow-hidden pr-6 select-none',
        {
          'z-10 bg-white': snapshot.isDragging || isActive,
          'bg-gray-100 hover:bg-gray-200': !snapshot.isDragging && !isActive
        }
      )}
      style={{
        ...style,
        width: `max(0px, calc(min(200px, ${100 / totalNum}%) - ${32 / totalNum}px))`
      }}
      title={tab.title || tab.url}
    >
      <div
        className="tab-icon mr-1 ml-1 flex h-6 w-6 flex-shrink-0 items-center justify-center"
        data-tab-id={tab.id}
      >
        {tab.isLoading ? (
          <div
            className="h-3 w-3 animate-spin rounded-full border border-gray-400 border-t-transparent"
            data-tab-id={tab.id}
          />
        ) : (
          faviconElement
        )}
      </div>

      <div className="tab-text min-w-0 flex-1 truncate text-sm text-gray-700" data-tab-id={tab.id}>
        {tab.alias || tab.title || tab.url}
      </div>

      {!isHome && (
        <button
          onMouseDown={(e) => e.stopPropagation()}
          onClick={(e) => onCloseTab(tab.id, e)}
          className="app-no-region tab-close-icon absolute right-1.5 z-20 flex h-5 w-5 items-center justify-center rounded-full opacity-0 transition-opacity group-hover:opacity-100 hover:bg-gray-200"
          title="关闭标签页"
        >
          <X size={12} />
        </button>
      )}
    </div>
  )
}
