@keyframes TackColor-Animation-Start {
  0% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0);
  }
  1.69% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0169);
  }
  3.38% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0338);
  }
  5.07% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0507);
  }
  6.76% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0676);
  }
  8.45% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0845);
  }
  10.14% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1014);
  }
  11.83% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1183);
  }
  13.52% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1352);
  }
  15.21% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1521);
  }
  16.9% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.169);
  }
  18.59% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1859);
  }
  20.28% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2028);
  }
  21.97% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2197);
  }
  23.66% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2366);
  }
  25.35% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2535);
  }
  27.04% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2704);
  }
  28.73% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2873);
  }
  30.42% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3042);
  }
  32.11% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3211);
  }
  33.8% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.338);
  }
  35.49% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3549);
  }
  37.18% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3718);
  }
  38.87% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3887);
  }
  40.56% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4056);
  }
  42.25% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4225);
  }
  43.94% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4394);
  }
  45.63% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4563);
  }
  47.32% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4732);
  }
  49.01% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4901);
  }
  50.7% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.507);
  }
  52.39% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5239);
  }
  54.08% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5408);
  }
  55.77% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5577);
  }
  57.46% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5746);
  }
  59.15% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5915);
  }
  60.84% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6084);
  }
  62.53% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6253);
  }
  64.22% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6422);
  }
  65.91% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6591);
  }
  67.6% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.676);
  }
  69.29% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6929);
  }
  70.98% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7098);
  }
  72.67% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7267);
  }
  74.36% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7436);
  }
  76.05% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7605);
  }
  77.74% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7774);
  }
  79.43% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7943);
  }
  81.12% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8112);
  }
  82.81% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8281);
  }
  84.5% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.845);
  }
  86.19% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8619);
  }
  87.88% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8788);
  }
  89.57% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8957);
  }
  91.26% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9126);
  }
  92.95% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9295);
  }
  94.64% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9464);
  }
  96.33% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9633);
  }
  98.02% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9802);
  }
  100% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 1);
  }
}

@keyframes TackColor-Animation-End {
  0% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 1);
  }
  1.69% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9831);
  }
  3.38% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9662);
  }
  5.07% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9493);
  }
  6.76% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9324);
  }
  8.45% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.9155);
  }
  10.14% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8986);
  }
  11.83% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8817);
  }
  13.52% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8648);
  }
  15.21% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8479);
  }
  16.9% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.831);
  }
  18.59% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.8141);
  }
  20.28% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7972);
  }
  21.97% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7803);
  }
  23.66% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7634);
  }
  25.35% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7465);
  }
  27.04% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7296);
  }
  28.73% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.7127);
  }
  30.42% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6958);
  }
  32.11% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6789);
  }
  33.8% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.662);
  }
  35.49% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6451);
  }
  37.18% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6282);
  }
  38.87% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.6113);
  }
  40.56% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5944);
  }
  42.25% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5775);
  }
  43.94% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5606);
  }
  45.63% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5437);
  }
  47.32% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5268);
  }
  49.01% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.5099);
  }
  50.7% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.493);
  }
  52.39% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4761);
  }
  54.08% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4592);
  }
  55.77% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4423);
  }
  57.46% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4254);
  }
  59.15% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.4085);
  }
  60.84% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3916);
  }
  62.53% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3747);
  }
  64.22% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3578);
  }
  65.91% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3409);
  }
  67.6% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.324);
  }
  69.29% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.3071);
  }
  70.98% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2902);
  }
  72.67% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2733);
  }
  74.36% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2564);
  }
  76.05% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2395);
  }
  77.74% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2226);
  }
  79.43% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.2057);
  }
  81.12% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1888);
  }
  82.81% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1719);
  }
  84.5% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.155);
  }
  86.19% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1381);
  }
  87.88% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1212);
  }
  89.57% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.1043);
  }
  91.26% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0874);
  }
  92.95% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0705);
  }
  94.64% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0536);
  }
  96.33% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0367);
  }
  98.02% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0.0198);
  }
  100% {
    --CustomScrollBar-TackColor: rgba(var(--CustomScrollBar-TackColor-Base), 0);
  }
}

.CustomScrollBar-root {
  --ScrollBar-width: 8px;
  --ScrollBar-padding: 1px;
  overscroll-behavior: contain;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-gutter: stable;
  --CustomScrollBar-TackColor: transparent;
  animation: TackColor-Animation-End 0.275s;
  animation-fill-mode: forwards;
}

.CustomScrollBar-root:hover {
  animation: TackColor-Animation-Start 0.275s;
  animation-fill-mode: forwards;
}
.CustomScrollBar-root::-webkit-scrollbar-thumb {
  background-color: var(--CustomScrollBar-TackColor);
  border-radius: calc(var(--ScrollBar-width) / 2);
  border: var(--ScrollBar-padding) solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
}

.CustomScrollBar-root::-webkit-scrollbar-thumb:hover {
  background-color: rgb(217, 221, 231);
}

.CustomScrollBar-root::-webkit-scrollbar-track {
  background-color: transparent;
}
.CustomScrollBar-root::-webkit-scrollbar {
  width: var(--ScrollBar-width);
}
