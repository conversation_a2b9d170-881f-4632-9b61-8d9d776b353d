import { CloseButton } from '@renderer/components/CloseButton'
import { MaximizeButton } from '@renderer/components/MaximizeButton'
import { MinimizeButton } from '@renderer/components/MinimizeButton'
import { useWindowState } from '@renderer/common/WindowStateContext'
import { isMac } from 'electron-global/platform'
import clsx from 'clsx'

function AppTitleBarWin({
  children,
  className
}: {
  children?: React.ReactNode
  className?: string
}) {
  const { isMaximized, maximize, minimize, close } = useWindowState()

  return (
    <div className={clsx('app-title-bar app-region', className)}>
      {children}
      <div className="flex shrink-0">
        <MinimizeButton onClick={minimize} />
        <MaximizeButton onClick={maximize} isMaximized={isMaximized} />
        <CloseButton onClick={close} />
      </div>
    </div>
  )
}

function AppTitleBarMac({
  children,
  className
}: {
  children?: React.ReactNode
  className?: string
}) {
  return <div className={clsx('app-title-bar app-region mac-title-bar', className)}>{children}</div>
}

export const AppTitleBar = isMac ? AppTitleBarMac : AppTitleBarWin
