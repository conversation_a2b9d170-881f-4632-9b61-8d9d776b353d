import type { SetStateAction } from 'react'
import { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react'
import { webviewManagerAPI } from '../bridge/webview'
import type { TabInfo } from 'electron-global/webview'

export interface TabsState {
  tabs: TabInfo[]
  activeTab: TabInfo
}

export function useTabs() {
  const [tabsInfo, setTabsInfo] = useState<TabsState>({
    tabs: [],
    activeTab: {} as TabInfo
  })

  useLayoutEffect(() => {
    const init = async () => {
      const tabs = await webviewManagerAPI.getAllTabs()
      const activeTab = await webviewManagerAPI.getActiveTab()
      setTabsInfo({ tabs, activeTab: activeTab || tabs[0] })
    }
    void init()
  }, [])

  useEffect(() => {
    const onTabsChanged = (newTabs: Record<string, TabInfo>, activeTab: TabInfo) => {
      setTabsInfo((prev) => {
        const _newTabs = { ...newTabs }
        const _tabs = []
        prev.tabs.forEach((tab) => {
          if (_newTabs[tab.id]) {
            _tabs.push(_newTabs[tab.id])
            delete _newTabs[tab.id]
          }
        })
        _tabs.push(...Object.values(_newTabs))

        return {
          ...prev,
          tabs: _tabs,
          activeTab
        }
      })
    }
    webviewManagerAPI.on('tabs-changed', onTabsChanged)

    return () => {
      webviewManagerAPI.off('tabs-changed', onTabsChanged)
    }
  }, [])

  const setTabs = useCallback(
    (tabs: SetStateAction<TabsState>) => {
      setTabsInfo(tabs)
    },
    [setTabsInfo]
  )

  return useMemo<[typeof tabsInfo, typeof setTabs]>(() => [tabsInfo, setTabs], [tabsInfo, setTabs])
}
