import React from 'react'

import { AppTitleBar } from '../components/AppTitleBar'
import { webviewManagerAPI } from '../common/bridge/webview'
import { useTabs } from '../common/hooks/useTabs'

import { TabBar } from './BrowserTabBar'
import type { TabInfo } from 'electron-global/webview'
import { BrowserTool } from './BrowserTool'
import { EllipsisVertical } from 'lucide-react'
import { ipcRenderer } from '@renderer/common/bridge'

export function Browser() {
  const [tabsInfo, setTabsInfo] = useTabs()

  const handleCreateNewTab = () => {
    void webviewManagerAPI.createNewTabPage()
  }

  const onCloseTab = (tabId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    void webviewManagerAPI.closeTab(tabId)
  }

  const onTabClick = (tabId: string) => {
    void webviewManagerAPI.switchToTab(tabId)
  }

  return (
    <div className="app-root flex h-screen flex-col">
      <AppTitleBar className="w-full bg-gray-100">
        <TabBar
          currentTabs={tabsInfo}
          onTabClick={onTabClick}
          onCloseTab={onCloseTab}
          onCreateNewTab={handleCreateNewTab}
          onTabsReordered={(newTabs: TabInfo[]) => {
            setTabsInfo((prev) => {
              return {
                ...prev,
                tabs: newTabs
              }
            })
          }}
        />
        {!__IS_PROD__ && (
          <button
            onClick={() => {
              void ipcRenderer.invoke('dev-tools')
            }}
            className="app-no-region rounded p-1 transition-colors hover:bg-gray-200"
            title="更多"
          >
            <EllipsisVertical size={16} />
          </button>
        )}
      </AppTitleBar>
      <div className="relative">
        <BrowserTool activeTab={tabsInfo.activeTab} />
      </div>
    </div>
  )
}
