import { getClientState, getSocketSeverState } from '@renderer/common/bridge'

import { Globe } from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'

export function Online() {
  const [clientConnected, setClientConnected] = useState(0)
  const [serverConnected, setServerConnected] = useState(false)
  const [port, setPort] = useState(0)
  const [deviceId, setDeviceId] = useState('')

  const onUpdateEvent = useCallback((_: Electron.IpcRendererEvent, data: Placeholder) => {
    setClientConnected(data)
  }, [])

  const onUpdateServerEvent = useCallback((_: Electron.IpcRendererEvent, data: Placeholder) => {
    setServerConnected(data)
  }, [])

  useEffect(() => {
    const params = new URLSearchParams(window.location.search)

    const init = async () => {
      const res = await getSocketSeverState()
      const res1 = await getClientState()

      setServerConnected(res)
      setClientConnected(res1)
    }

    void init()
    setPort(parseInt(params.get('port') || '30020'))
    setDeviceId(params.get('deviceId') || '')
  }, [onUpdateEvent, onUpdateServerEvent])

  return (
    <div className="relative flex flex-1 flex-col items-center justify-center bg-white text-gray-400">
      <Globe size={48} className="mx-auto mb-4 opacity-50" />
      <div className="space-y-2">
        <div className="flex min-w-48 items-center justify-between gap-4">
          <span className="text-lg">客户端状态:</span>
          <span className={`text-lg ${clientConnected ? 'text-green-500' : ''}`}>
            {clientConnected ? `已连接-${clientConnected}` : '等待连接'}
          </span>
        </div>
        <div className="flex min-w-48 items-center justify-between gap-4">
          <span className="text-lg">服务端状态:</span>
          <span className={`text-lg ${serverConnected ? 'text-green-500' : ''}`}>
            {serverConnected ? '已连接' : '等待连接'}
          </span>
        </div>
        <div className="flex min-w-48 items-center justify-between gap-4">
          <span className="text-lg">连接端口:</span>
          <span className="text-lg">{port}</span>
        </div>
        <div className="flex min-w-48 items-center justify-between gap-4">
          <span className="text-lg">设备标识:</span>
          <span className="text-lg">{deviceId}</span>
        </div>
        <div className="flex min-w-48 items-center justify-between gap-4">
          <span className="text-lg">当前版本:</span>
          <span className="text-lg">{__APP_VERSION__}</span>
        </div>
      </div>
    </div>
  )
}
