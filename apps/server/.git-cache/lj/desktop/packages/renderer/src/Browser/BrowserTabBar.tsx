import React, { useState } from 'react'
import { Plus } from 'lucide-react'
import clsx from 'clsx'
import type { TabInfo } from 'electron-global/webview'
import type { DropResult } from '@hello-pangea/dnd'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'
import type { TabsState } from '@renderer/common/hooks/useTabs'
import { BrowserTabItem } from './BrowserTabItem' // 引入新的子组件
import { isMac } from 'electron-global/platform'

interface TabBarProps {
  currentTabs: TabsState
  onTabClick: (tabId: string) => void
  onCloseTab: (tabId: string, event: React.MouseEvent) => void
  onCreateNewTab: () => void
  onTabsReordered: (newTabs: TabInfo[]) => void
  className?: string
}

// 定义一些常量，避免“魔法数字”
const CAPTURE_BUTTONS_WIDTH = 138
const DRAGGABLE_AREA_WIDTH = 60

export const TabBar: React.FC<TabBarProps> = ({
  currentTabs,
  onTabClick,
  onCloseTab,
  onCreateNewTab,
  onTabsReordered,
  className
}) => {
  const [isDragging, setIsDragging] = useState(false)

  const onDragEnd = (result: DropResult) => {
    setIsDragging(false)
    if (!result.destination || result.destination.index === result.source.index) {
      return
    }

    const newTabs = Array.from(currentTabs.tabs)
    const [reorderedItem] = newTabs.splice(result.source.index, 1)
    newTabs.splice(result.destination.index, 0, reorderedItem)
    onTabsReordered(newTabs)
  }

  const onDragStart = ({ draggableId }: { draggableId: string }) => {
    onTabClick(draggableId)
    setIsDragging(true)
  }

  return (
    <div
      style={{
        width: isMac
          ? `calc(100% - ${DRAGGABLE_AREA_WIDTH - 20}px)`
          : `calc(100% - ${CAPTURE_BUTTONS_WIDTH}px - ${DRAGGABLE_AREA_WIDTH}px)`
      }}
      className="h-full"
    >
      <DragDropContext onDragEnd={onDragEnd} onDragStart={onDragStart}>
        <Droppable droppableId="tab-bar" direction="horizontal">
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={clsx(
                'relative flex h-full max-w-[100%] items-center overflow-hidden',
                className
              )}
            >
              {currentTabs.tabs.map((tab, index) => (
                <Draggable key={tab.id} draggableId={tab.id} index={index}>
                  {(provided, snapshot) => (
                    <BrowserTabItem
                      tab={tab}
                      isHome={tab.isHome}
                      totalNum={currentTabs.tabs.length}
                      isActive={tab.id === currentTabs.activeTab?.id}
                      isDragging={isDragging}
                      provided={provided}
                      snapshot={snapshot}
                      onTabClick={onTabClick}
                      onCloseTab={onCloseTab}
                    />
                  )}
                </Draggable>
              ))}

              {!isDragging && (
                <button
                  onClick={onCreateNewTab}
                  className={clsx(
                    'app-no-region none flex h-full w-8 flex-shrink-0 items-center justify-center transition-colors hover:bg-gray-200',
                    { 'opacity-0': isDragging }
                  )}
                  title="新建标签页"
                >
                  <Plus size={14} />
                </button>
              )}

              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  )
}
