import { useLayoutEffect } from 'react'
import { ipc<PERSON><PERSON><PERSON> } from '../bridge'

/**
 * 监听主进程来的事件
 * @param channel
 * @param callback
 */
export function useIpcEvent<Value = Placeholder>(
  channel: string,
  callback: (event: AnyObject, data: Value) => void
) {
  useLayoutEffect(() => {
    ipcRenderer.on(channel, callback)
    return () => {
      ipcRenderer.removeListener(channel, callback)
    }
  }, [])
}
