import { twMerge } from 'tailwind-merge'
import { Loading } from './Loading'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'primary' | 'success' | 'danger'
  outlined?: boolean
  loading?: boolean
  onClick?: () => void | Promise<void>
  ref?: React.Ref<HTMLButtonElement>
}

export function Button({
  children,
  onClick,
  loading,
  className,
  outlined,
  variant = 'default',
  disabled,
  ...others
}: ButtonProps) {
  const variantStyles = {
    default: {
      solid: 'bg-[#5048E5] text-white hover:bg-[#603af9]',
      outlined: 'text-[#5048E5] border-1 border-[#5048E5] hover:text-[#603af9]',
      disabled: {
        solid: 'bg-gray-300 text-gray-500 cursor-not-allowed',
        outlined: 'text-gray-400 border-1 border-gray-300 cursor-not-allowed'
      }
    },
    primary: {
      solid: 'bg-blue-500 text-white hover:bg-blue-600',
      outlined: 'text-blue-500 border-1 border-blue-500 hover:bg-blue-500 hover:text-white',
      disabled: {
        solid: 'bg-gray-300 text-gray-500 cursor-not-allowed',
        outlined: 'text-gray-400 border-1 border-gray-300 cursor-not-allowed'
      }
    },
    success: {
      solid: 'bg-green-500 text-white hover:bg-green-600',
      outlined: 'text-green-500 border-1 border-green-500 hover:bg-green-500 hover:text-white',
      disabled: {
        solid: 'bg-gray-300 text-gray-500 cursor-not-allowed',
        outlined: 'text-gray-400 border-1 border-gray-300 cursor-not-allowed'
      }
    },
    danger: {
      solid: 'bg-red-500 text-white hover:bg-red-600',
      outlined: 'text-red-500 border-1 border-red-500 hover:bg-red-500 hover:text-white',
      disabled: {
        solid: 'bg-gray-300 text-gray-500 cursor-not-allowed',
        outlined: 'text-gray-400 border-1 border-gray-300 cursor-not-allowed'
      }
    }
  }

  const styleForVariant = variantStyles[variant] || variantStyles.default

  // 根据disabled状态选择样式
  const variantStyle = disabled
    ? styleForVariant.disabled[outlined ? 'outlined' : 'solid']
    : styleForVariant[outlined ? 'outlined' : 'solid']

  return (
    <button
      {...others}
      disabled={disabled || loading}
      className={twMerge(
        `relative flex h-[32px] items-center justify-center rounded-lg px-4 transition-transform ${
          disabled || loading ? '' : 'cursor-pointer active:scale-95'
        } ${variantStyle}`,
        className
      )}
      onClick={async () => {
        if (!loading && !disabled) {
          await onClick?.()
        }
      }}
    >
      {loading && (
        <Loading
          color="blue"
          bgColor="#fff"
          style={{
            position: 'absolute'
          }}
        />
      )}
      <div className={loading ? 'opacity-0' : ''}>{children}</div>
    </button>
  )
}
