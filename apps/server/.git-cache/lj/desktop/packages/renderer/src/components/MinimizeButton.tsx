export function MinimizeButton(props: React.HTMLAttributes<HTMLButtonElement>) {
  return (
    <button className="button-caption" {...props} onContextMenu={(e) => e.preventDefault()}>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" height={16}>
        <path
          d="M3.49805,8 C3.42969,8 3.36458,7.98698 3.30273,7.96094 C3.24414,7.9349 3.19206,7.89909 3.14648,7.85352 C3.10091,7.80794 3.0651,7.75586 3.03906,7.69727 C3.01302,7.63542 3,7.57031 3,7.50195 C3,7.43359 3.01302,7.37012 3.03906,7.31152 C3.0651,7.24967 3.10091,7.19596 3.14648,7.15039 C3.19206,7.10156 3.24414,7.06413 3.30273,7.03809 C3.36458,7.01204 3.42969,6.99902 3.49805,6.99902 L12.502,6.99902 C12.5703,6.99902 12.6338,7.01204 12.6924,7.03809 C12.7542,7.06413 12.8079,7.10156 12.8535,7.15039 C12.8991,7.19596 12.9349,7.24967 12.9609,7.31152 C12.987,7.37012 13,7.43359 13,7.50195 C13,7.57031 12.987,7.63542 12.9609,7.69727 C12.9349,7.75586 12.8991,7.80794 12.8535,7.85352 C12.8079,7.89909 12.7542,7.9349 12.6924,7.96094 C12.6338,7.98698 12.5703,8 12.502,8 Z"
          fill="currentColor"
        />
      </svg>
    </button>
  )
}
