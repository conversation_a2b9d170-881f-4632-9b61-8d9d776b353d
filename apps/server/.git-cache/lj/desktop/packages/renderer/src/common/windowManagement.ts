import { noop } from 'electron-global/noop'
import type { JSX } from 'react'
import { createRoot } from 'react-dom/client'

window.simpleEvent = {}
/**
 * 当前打开的窗口
 */
export const windowManagement = {} as Record<string, Window>

/**
 * 创建窗口
 * @param page
 * @param param1
 * @param config
 * @returns
 */
export function createWindow<T extends AnyObject>(
  page: string,
  {
    state,
    callback,
    windowId = ''
  }: { state: T; callback?: (...args: Placeholder) => void; windowId?: string },
  config?: Record<string, Placeholder>
): Window {
  const windowHashId = `${windowId}`
  const turnWindow = windowManagement[windowHashId]
  /**
   * 如果有直接返回
   * 排除可以多开的窗口
   */
  if (turnWindow) {
    return turnWindow
  }

  const featuresConfig: Record<string, Placeholder> = { ...config }

  const features = Object.keys(featuresConfig).reduce((memo, current) => {
    memo += `${current}=${featuresConfig[current]},`
    return memo
  }, '')

  let win: Window | null = window.open(page, '_blank', features)!
  /**
   * 记录子窗口
   */
  windowManagement[windowHashId] = win

  /**
   * 初始化页面state
   */
  window.simpleEvent[windowHashId] = state

  /**
   * 共享 preload
   * 不能通过 主进程 config 配置,
   */
  win.app = {
    get windowId() {
      return windowHashId
    },

    get ipcRenderer() {
      return {}
    }
  } as typeof window.app & { windowId: string }

  /**
   * 卸载事件,
   * 回收资源
   */
  // eslint-disable-next-line @typescript-eslint/unbound-method
  const originalClose = win.close
  win.close = noop
  win.onClose = (...args) => {
    originalClose()
    delete windowManagement[windowHashId]
    win = null
    callback && callback(...args)
  }
  return win
}

/**
 * 渲染 子窗口
 * @param element
 */
export function renderChildWindow(element: () => JSX.Element) {
  const container = document.getElementById('root') as HTMLElement
  const root = createRoot(container)

  root.render(element())
}
