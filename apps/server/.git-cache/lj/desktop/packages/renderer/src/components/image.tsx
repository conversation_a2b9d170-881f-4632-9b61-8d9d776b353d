import React, { useState } from 'react'

export function ImageComponent({
  src,
  children,
  className,
  style,
  ...props
}: {
  src?: string
  children?: React.ReactNode
} & React.ImgHTMLAttributes<HTMLImageElement>) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageLoad = () => {
    setImageLoading(false)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageLoading(false)
    setImageError(true)
  }

  return (
    <div className={`relative flex items-center justify-center ${className}`} style={style}>
      {!imageError ? (
        <img
          src={src}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          {...props}
        />
      ) : (
        children
      )}
    </div>
  )
}
