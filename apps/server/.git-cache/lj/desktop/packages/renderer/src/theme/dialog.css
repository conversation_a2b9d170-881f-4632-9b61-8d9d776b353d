.dialog-root {
  view-transition-name: dialog;
  position: fixed;
  inset: 0;
  height: 100%;
  width: 100%;
  max-width: 100%;
  max-height: 100%;
  background-color: transparent;
}

.dialog-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: none;
  border-radius: 12px;
  padding: 0;
  background: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-width: 200px;
  min-height: 200px;
}

.dialog-root::backdrop {
  display: none;
}

/* 打开动画 */
::view-transition-new(dialog) {
  animation: fade-in 0.275s ease forwards;
}

/* 关闭动画 */
::view-transition-old(dialog) {
  animation: fade-out 0.275s ease forwards;
}
