@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none;
  }

  html,
  body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-size: 14px;
    font-family:
      ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
      'Segoe UI Symbol', 'Noto Color Emoji';
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
  }

  /* 移除列表默认样式 */
  ul,
  ol {
    list-style: none;
  }

  /* 移除链接的默认下划线 */
  a {
    text-decoration: none;
    color: inherit;
  }

  /* 移除按钮的默认样式 */
  button,
  input,
  textarea,
  iframe {
    border: none;
    outline: none;
  }

  /* 确保图片不会超出容器 */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* 统一表单元素样式 */
  input,
  textarea,
  button,
  select {
    font: inherit;
  }

  /* 确保表格布局正常 */
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
}
