import { useState, useEffect, useCallback } from 'react'
import { ipc<PERSON>enderer } from '../bridge'

export type UpdaterEvent =
  | 'checking-for-update'
  | 'update-available'
  | 'update-not-available'
  | 'error'
  | 'download-progress'
  | 'update-downloaded'

export interface UpdateInfo {
  version: string
  releaseDate?: string
  releaseName?: string
}

export interface ProgressInfo {
  bytesPerSecond: number
  percent: number
  transferred: number
  total: number
}

export interface UpdaterEventData {
  event: UpdaterEvent
  data?: UpdateInfo | Error | ProgressInfo
  autoDownload: boolean
  description?: string
}

export interface UpdaterState {
  currentEvent: UpdaterEvent | null
  isChecking: boolean
  isDownloading: boolean
  hasUpdate: boolean
  hasError: boolean

  // 数据
  updateInfo: UpdateInfo | null
  progress: ProgressInfo | null
  error: Error | null
  autoDownload: boolean
  description?: string
}

export interface UpdaterActions {
  checkForUpdate: () => void
  downloadUpdate: () => void
  quitAndInstall: () => void
  clearError: () => void
  reset: () => void
}

const initialState: UpdaterState = {
  currentEvent: null,
  isChecking: false,
  isDownloading: false,
  hasUpdate: false,
  hasError: false,
  updateInfo: null,
  progress: null,
  error: null,
  autoDownload: false
}

export function useAutoUpdater() {
  const [state, setState] = useState<UpdaterState>(initialState)

  const onUpdateEvent = useCallback((eventData: UpdaterEventData) => {
    const { event, data, autoDownload, description } = eventData
    if (import.meta.env.DEV) {
      console.log(event)
    }
    setState((prevState) => {
      const newState: UpdaterState = {
        ...prevState,
        currentEvent: event,
        autoDownload,
        hasError: false,
        error: null,
        description
      }

      switch (event) {
        case 'checking-for-update':
          return {
            ...newState,
            isChecking: true,
            hasUpdate: false,
            updateInfo: null,
            progress: null
          }

        case 'update-available':
          return {
            ...newState,
            isChecking: false,
            hasUpdate: true,
            updateInfo: data as UpdateInfo,
            isDownloading: autoDownload
          }

        case 'update-not-available':
          return {
            ...newState,
            isChecking: false,
            hasUpdate: false,
            updateInfo: data as UpdateInfo
          }

        case 'error':
          return {
            ...newState,
            isChecking: false,
            isDownloading: false,
            hasError: true,
            error: data as Error
          }

        case 'download-progress':
          return {
            ...newState,
            isDownloading: true,
            progress: data as ProgressInfo
          }

        case 'update-downloaded':
          return {
            ...newState,
            isDownloading: false,
            updateInfo: data as UpdateInfo,
            progress: { ...prevState.progress!, percent: 100 }
          }

        default:
          return newState
      }
    })
  }, [])

  const checkForUpdate = useCallback(() => {
    ipcRenderer.send('check-for-update')
  }, [])

  const downloadUpdate = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      isDownloading: true
    }))
    void ipcRenderer.invoke('autoUpdate', { type: 'downloadUpdate' })
  }, [])

  const quitAndInstall = useCallback(() => {
    void ipcRenderer.invoke('autoUpdate', { type: 'quitAndInstall' })
  }, [])

  const clearError = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      hasError: false,
      error: null,
      currentEvent: null
    }))
  }, [])

  const reset = useCallback(() => {
    setState(initialState)
  }, [])

  useEffect(() => {
    const onEvent = (_: Electron.IpcRendererEvent, data: Placeholder) => {
      onUpdateEvent(data)
    }

    ipcRenderer.on('autoUpdate', onEvent)
    void ipcRenderer.invoke('update-window-read')
    return () => {
      ipcRenderer.removeListener('autoUpdate', onEvent)
    }
  }, [onUpdateEvent])

  const isUpdateReady = state.currentEvent === 'update-downloaded'
  const canInstall = isUpdateReady && !state.isChecking && !state.isDownloading
  const downloadProgress = state.progress?.percent || 0

  const actions: UpdaterActions = {
    checkForUpdate,
    downloadUpdate,
    quitAndInstall,
    clearError,
    reset
  }

  return {
    // 状态
    ...state,
    // 派生状态
    isUpdateReady,
    canInstall,
    downloadProgress,
    // 操作方法
    actions
  }
}
