import { twMerge } from 'tailwind-merge'

export interface ILoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: number
  bgColor?: React.CSSProperties['color']
  color?: React.CSSProperties['color']
  strokeWidth?: number
}

export function Loading(props: ILoadingProps) {
  return (
    <div
      className={twMerge(`loading`, props.className)}
      style={{
        width: `${props.size}px`,
        height: `${props.size}px`,
        borderColor: props.color,
        borderTopColor: props.bgColor,
        borderRightColor: props.bgColor,
        borderBottomColor: props.bgColor,
        borderWidth: `${props.strokeWidth}px`,
        ...props.style
      }}
    />
  )
}
