export function CloseButton({
  className,
  ...other
}: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      className={`close-button-caption ${className ?? ''}`}
      {...other}
      onContextMenu={(e) => e.preventDefault()}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" height={16}>
        <path
          d="M8,8.70801 L3.85449,12.8535 C3.75684,12.9512 3.63965,13 3.50293,13 C3.3597,13 3.23926,12.9528 3.1416,12.8584 C3.0472,12.7607 3,12.6403 3,12.4971 C3,12.3604 3.04883,12.2432 3.14648,12.1455 L7.29199,8 L3.14648,3.85449 C3.04883,3.75684 3,3.63802 3,3.49805 C3,3.42969 3.01302,3.36458 3.03906,3.30273 C3.0651,3.24089 3.10091,3.1888 3.14648,3.14648 C3.19206,3.10091 3.24577,3.0651 3.30762,3.03906 C3.36947,3.01302 3.43457,3 3.50293,3 C3.63965,3 3.75684,3.04883 3.85449,3.14648 L8,7.29199 L12.1455,3.14648 C12.2432,3.04883 12.362,3 12.502,3 C12.5703,3 12.6338,3.01302 12.6924,3.03906 C12.7542,3.0651 12.8079,3.10091 12.8535,3.14648 C12.8991,3.19206 12.9349,3.24577 12.9609,3.30762 C12.987,3.36621 13,3.42969 13,3.49805 C13,3.63802 12.9512,3.75684 12.8535,3.85449 L8.70801,8 L12.8535,12.1455 C12.9512,12.2432 13,12.3604 13,12.4971 C13,12.5654 12.987,12.6305 12.9609,12.6924 C12.9349,12.7542 12.8991,12.8079 12.8535,12.8535 C12.8112,12.8991 12.7591,12.9349 12.6973,12.9609 C12.6354,12.987 12.5703,13 12.502,13 C12.362,13 12.2432,12.9512 12.1455,12.8535 L8,8.70801 Z"
          fill="currentColor"
        />
      </svg>
    </button>
  )
}
