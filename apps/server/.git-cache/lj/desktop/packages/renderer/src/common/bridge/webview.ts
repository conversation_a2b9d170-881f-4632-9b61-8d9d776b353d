import type { TabInfo } from 'electron-global/webview'

export const { ipcRenderer } = window.app

export interface NavigationInfo {
  url: string
  title: string
  canGoBack: boolean
  canGoForward: boolean
  isLoading: boolean
  progress: number
}

/**
 * 渲染进程页面管理器
 * 提供与主进程 WebviewManager 对应的方法
 */
export class WebviewManager {
  private eventListeners: Map<string, ((...args: Placeholder) => void)[]> = new Map()
  private isSubscribed = false

  constructor() {
    this.setupEventListeners()
  }

  /**
   * 创建新标签页
   * @param url 要打开的URL，可选
   * @returns 返回新创建的标签页ID
   */
  async createNewTabPage(url?: string) {
    try {
      await ipcRenderer.invoke('page-manager:create-new-tab-page', url)
    } catch (error) {
      console.error('Failed to create tab:', error)
      throw error
    }
  }

  async saveCookies() {
    try {
      return ipcRenderer.invoke('page-manager:save-cookies') as Promise<boolean>
    } catch (error) {
      console.error('Failed to save cookies:', error)
      return false
    }
  }

  /**
   * 关闭标签页
   * @param tabId 要关闭的标签页ID
   * @returns 是否成功关闭
   */
  async closeTab(tabId: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:close-tab', tabId)
      return !!result
    } catch (error) {
      console.error('Failed to close tab:', error)
      return false
    }
  }

  /**
   * 切换到指定标签页
   * @param tabId 要切换到的标签页ID
   * @returns 是否成功切换
   */
  async switchToTab(tabId: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:switch-tab', tabId)
      return !!result
    } catch (error) {
      console.error('Failed to switch tab:', error)
      return false
    }
  }

  /**
   * 导航到指定URL
   * @param tabId 标签页ID
   * @param url 要导航到的URL
   * @returns 是否成功导航
   */
  async navigate(tabId: string, url: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:navigate', tabId, url)
      return !!result
    } catch (error) {
      console.error('Failed to navigate:', error)
      return false
    }
  }

  /**
   * 后退
   * @param tabId 标签页ID，可选，默认为当前活跃标签页
   * @returns 是否成功后退
   */
  async goBack(tabId?: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:go-back', tabId)
      return !!result
    } catch (error) {
      console.error('Failed to go back:', error)
      return false
    }
  }

  /**
   * 前进
   * @param tabId 标签页ID，可选，默认为当前活跃标签页
   * @returns 是否成功前进
   */
  async goForward(tabId?: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:go-forward', tabId)
      return !!result
    } catch (error) {
      console.error('Failed to go forward:', error)
      return false
    }
  }

  /**
   * 刷新页面
   * @param tabId 标签页ID，可选，默认为当前活跃标签页
   * @param ignoreCache 是否忽略缓存，默认false
   * @returns 是否成功刷新
   */
  async reload(tabId?: string, ignoreCache = false): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:reload', tabId, ignoreCache)
      return !!result
    } catch (error) {
      console.error('Failed to reload:', error)
      return false
    }
  }

  /**
   * 获取所有标签页信息
   * @returns 所有标签页的信息数组
   */
  async getAllTabs(): Promise<TabInfo[]> {
    try {
      const tabs = (await ipcRenderer.invoke('page-manager:get-all-tabs')) as TabInfo[]
      return tabs || []
    } catch (error) {
      console.error('Failed to get all tabs:', error)
      return []
    }
  }

  /**
   * 获取当前活跃标签页信息
   * @returns 当前活跃标签页信息，如果没有则返回null
   */
  async getActiveTab(): Promise<TabInfo | null> {
    try {
      const activeTab = (await ipcRenderer.invoke('page-manager:get-active-tab')) as TabInfo
      return activeTab
    } catch (error) {
      console.error('Failed to get active tab:', error)
      return null
    }
  }

  async getHomeTab(): Promise<TabInfo | null> {
    try {
      const homeTab = (await ipcRenderer.invoke('page-manager:get-home-tab')) as TabInfo
      return homeTab
    } catch (error) {
      console.error('Failed to get active tab:', error)
      return null
    }
  }

  /**
   * 搜索或导航
   * @param query 搜索查询或URL
   * @param tabId 标签页ID，可选，默认为当前活跃标签页
   * @returns 是否成功
   */
  async searchOrNavigate(query: string, tabId?: string): Promise<boolean> {
    try {
      const result = await ipcRenderer.invoke('page-manager:search-or-navigate', query, tabId)
      return !!result
    } catch (error) {
      console.error('Failed to search or navigate:', error)
      return false
    }
  }

  /**
   * 订阅主进程事件推送
   * 自动接收标签页和导航状态变化
   */
  async subscribeToEvents(): Promise<boolean> {
    if (this.isSubscribed) return true

    try {
      await ipcRenderer.invoke('page-manager:subscribe-events')
      this.isSubscribed = true
      return true
    } catch (error) {
      console.error('Failed to subscribe to events:', error)
      return false
    }
  }

  /**
   * 取消订阅事件推送
   */
  async unsubscribeFromEvents(): Promise<boolean> {
    if (!this.isSubscribed) return true

    try {
      await ipcRenderer.invoke('page-manager:unsubscribe-events')
      this.isSubscribed = false
      return true
    } catch (error) {
      console.error('Failed to unsubscribe from events:', error)
      return false
    }
  }

  // 事件监听方法

  /**
   * 监听事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on(eventName: string, callback: (...args: Placeholder) => void): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    this.eventListeners.get(eventName)!.push(callback)
  }

  /**
   * 移除事件监听
   * @param eventName 事件名称
   * @param callback 要移除的回调函数
   */
  off(eventName: string, callback: (...args: Placeholder) => void): void {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 移除所有事件监听
   * @param eventName 事件名称，可选，如果不提供则移除所有事件
   */
  removeAllListeners(eventName?: string): void {
    if (eventName) {
      this.eventListeners.delete(eventName)
    } else {
      this.eventListeners.clear()
    }
  }

  /**
   * 触发事件
   * @param eventName 事件名称
   * @param args 事件参数
   */
  private emit(eventName: string, ...args: Placeholder[]): void {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      listeners.forEach((callback) => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`Error in _event listener for ${eventName}:`, error)
        }
      })
    }
  }

  /**
   * 设置事件监听器，监听来自主进程的事件
   */
  private setupEventListeners(): void {
    // 监听标签页状态变化（实时推送）
    ipcRenderer.on('tabs-state-changed', (_, data: { tabs: TabInfo[]; activeTab: TabInfo }) => {
      this.emit(
        'tabs-changed',
        data.tabs.reduce((acc, tab) => ({ ...acc, [tab.id]: tab }), {}),
        data.activeTab
      )
    })

    // 监听导航状态变化（实时推送）
    ipcRenderer.on('navigation-state-changed', (_, data: { tabId: string } & NavigationInfo) => {
      this.emit('navigation-changed', data)
    })

    // 监听标签页事件
    ipcRenderer.on('tab-created', (_event, tab: TabInfo) => {
      this.emit('tab-created', tab)
    })

    ipcRenderer.on('tab-switched', (_event, tab: TabInfo) => {
      this.emit('tab-switched', tab)
    })

    ipcRenderer.on('tab-updated', (_event, tab: TabInfo) => {
      this.emit('tab-updated', tab)
    })

    ipcRenderer.on('tab-closed', (_event, tabId: string) => {
      this.emit('tab-closed', tabId)
    })

    // 监听导航事件
    ipcRenderer.on('navigation-started', (_event, tabId: string, url: string) => {
      this.emit('navigation-started', tabId, url)
    })

    ipcRenderer.on('navigation-action', (_event, tabId: string, action: string) => {
      this.emit('navigation-action', tabId, action)
    })

    // 监听加载事件
    ipcRenderer.on('loading-started', (_event, tabId: string) => {
      this.emit('loading-started', tabId)
    })

    ipcRenderer.on('loading-finished', (_event, tabId: string) => {
      this.emit('loading-finished', tabId)
    })

    ipcRenderer.on('loading-failed', (_event, tabId: string, error: Error) => {
      this.emit('loading-failed', tabId, error)
    })

    ipcRenderer.on('loading-progress', (_event, tabId: string, progress: number) => {
      this.emit('loading-progress', tabId, progress)
    })

    // 监听其他事件
    ipcRenderer.on('will-navigate', (_event, tabId: string, url: string) => {
      this.emit('will-navigate', tabId, url)
    })
  }

  /**
   * 销毁管理器，清理所有事件监听
   */
  async destroy(): Promise<void> {
    // 取消订阅
    await this.unsubscribeFromEvents()

    this.removeAllListeners()

    // 移除 IPC 监听器
    ipcRenderer.removeAllListeners('tabs-state-changed')
    ipcRenderer.removeAllListeners('navigation-state-changed')
    ipcRenderer.removeAllListeners('tab-created')
    ipcRenderer.removeAllListeners('tab-switched')
    ipcRenderer.removeAllListeners('tab-updated')
    ipcRenderer.removeAllListeners('tab-closed')
    ipcRenderer.removeAllListeners('navigation-started')
    ipcRenderer.removeAllListeners('navigation-action')
    ipcRenderer.removeAllListeners('loading-started')
    ipcRenderer.removeAllListeners('loading-finished')
    ipcRenderer.removeAllListeners('loading-failed')
    ipcRenderer.removeAllListeners('loading-progress')
    ipcRenderer.removeAllListeners('will-navigate')
  }
}

// 创建单例实例
export const webviewManagerAPI = new WebviewManager()

ipcRenderer.on('init-webview-manager', () => {
  void webviewManagerAPI.subscribeToEvents()
})
