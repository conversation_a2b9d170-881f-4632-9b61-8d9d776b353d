import { AlertTriangle, WifiOff } from 'lucide-react'

const ErrorPage = () => {
  const getErrorInfoFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search)
    return {
      errorCode: parseInt(urlParams.get('errorCode') || '-1'),
      errorDescription: urlParams.get('errorDescription') || 'UNKNOWN_ERROR',
      url: urlParams.get('originalUrl') || window.location.href,
      timestamp: urlParams.get('timestamp')
        ? new Date(urlParams.get('timestamp')!).toLocaleString('zh-CN')
        : new Date().toLocaleString('zh-CN')
    }
  }

  const errorInfo = getErrorInfoFromUrl()

  const getErrorIcon = () => {
    if (
      errorInfo.errorDescription.includes('INTERNET') ||
      errorInfo.errorDescription.includes('NETWORK')
    ) {
      return <WifiOff className="h-12 w-12 text-red-500" />
    }
    return <AlertTriangle className="h-12 w-12 text-red-500" />
  }

  const getErrorTitle = () => {
    if (
      errorInfo.errorDescription.includes('INTERNET') ||
      errorInfo.errorDescription.includes('NETWORK')
    ) {
      return '网络连接失败'
    }
    if (errorInfo.errorDescription.includes('DNS')) {
      return 'DNS解析失败'
    }
    if (errorInfo.errorDescription.includes('TIMEOUT')) {
      return '连接超时'
    }
    return '页面加载失败'
  }

  const getErrorMessage = () => {
    if (
      errorInfo.errorDescription.includes('INTERNET') ||
      errorInfo.errorDescription.includes('NETWORK')
    ) {
      return '请检查您的网络连接，然后重试。'
    }
    if (errorInfo.errorDescription.includes('DNS')) {
      return '无法解析域名，请检查网络设置或稍后重试。'
    }
    if (errorInfo.errorDescription.includes('TIMEOUT')) {
      return '服务器响应超时，请稍后重试。'
    }
    return '无法加载此页面，请检查网络连接或稍后重试。'
  }

  return (
    <div className="flex min-h-screen justify-center pt-[20vh]">
      <div className="flex w-full max-w-md flex-col gap-4">
        {/* 错误图标区域 */}
        <div className="flex flex-col items-center gap-2">
          <div className="inline-flex h-20 w-20 items-center justify-center rounded-full bg-red-50">
            {getErrorIcon()}
          </div>

          <h1 className="text-2xl font-bold text-gray-900">{getErrorTitle()}</h1>

          <p className="text-sm leading-relaxed text-gray-600">{getErrorMessage()}</p>
        </div>

        {/* 错误详情卡片 */}
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <div className="space-y-4 text-gray-600">
            <div className="flex justify-between">
              <span>错误代码:</span>
              <span className="rounded bg-gray-100 px-1 font-mono">{errorInfo.errorCode}</span>
            </div>
            <div className="flex justify-between">
              <span>错误描述:</span>
              <span>{errorInfo.errorDescription}</span>
            </div>
            <div className="flex justify-between">
              <span>请求地址:</span>
              <span className="ml-2 flex-1 cursor-pointer text-right break-all text-blue-600 hover:text-blue-800">
                {errorInfo.url}
              </span>
            </div>
            <div className="flex justify-between">
              <span>时间:</span>
              <span>{errorInfo.timestamp}</span>
            </div>
          </div>
        </div>

        {/* 帮助提示 */}
        <div className="text-center">
          <p className="text-xs leading-relaxed text-gray-500">
            如果问题持续存在，请检查网络设置或联系技术支持
          </p>
        </div>
      </div>
    </div>
  )
}

export { ErrorPage }
