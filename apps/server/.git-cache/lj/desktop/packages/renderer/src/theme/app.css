.app-region {
  -webkit-app-region: drag;
}
.app-no-region {
  -webkit-app-region: no-drag;
}

:root {
  --CustomScrollBar-TackColor-Base: 228, 231, 237;
  /* Base Colors */
  --light-base-normal-color: transparent;
  --dark-base-normal-color: #ffffff;

  /* Default Button Background Colors */
  --light-button-background-normal-color: transparent;
  --light-button-background-hovered-color: rgba(0, 0, 0, 0.0373);
  --light-button-background-pressed-color: rgba(0, 0, 0, 0.0241);

  --dark-button-background-normal-color: transparent;
  --dark-button-background-hovered-color: rgba(255, 255, 255, 0.0605);
  --dark-button-background-pressed-color: rgba(255, 255, 255, 0.0419);

  /* Default Button Icon Colors */
  --light-button-icon-normal-color: rgba(0, 0, 0, 0.8956);
  --light-button-icon-hovered-color: rgba(0, 0, 0, 0.8956);
  --light-button-icon-pressed-color: rgba(0, 0, 0, 0.6063);
  --light-button-icon-disabled-color: rgba(0, 0, 0, 0.3614);

  --dark-button-icon-normal-color: #ffffff;
  --dark-button-icon-hovered-color: #ffffff;
  --dark-button-icon-pressed-color: rgba(255, 255, 255, 0.786);
  --dark-button-icon-disabled-color: rgba(255, 255, 255, 0.3628);

  /* Close Button Background Colors */
  --light-close-button-background-normal-color: transparent;
  --light-close-button-background-hovered-color: #c42b1c;
  --light-close-button-background-pressed-color: rgba(196, 43, 28, 0.9);

  --dark-close-button-background-normal-color: transparent;
  --dark-close-button-background-hovered-color: #c42b1c;
  --dark-close-button-background-pressed-color: rgba(196, 43, 28, 0.9);

  /* Close Button Icon Colors */
  --light-close-button-icon-normal-color: rgba(0, 0, 0, 0.8956);
  --light-close-button-icon-hovered-color: #ffffff;
  --light-close-button-icon-pressed-color: rgba(255, 255, 255, 0.7);
  --light-close-button-icon-disabled-color: rgba(0, 0, 0, 0.3614);

  --dark-close-button-icon-normal-color: #ffffff;
  --dark-close-button-icon-hovered-color: #ffffff;
  --dark-close-button-icon-pressed-color: rgba(255, 255, 255, 0.786);
  --dark-close-button-icon-disabled-color: rgba(255, 255, 255, 0.3628);
}

@layer base {
  #root {
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(
      to bottom left,
      white,
      white,
      rgb(239, 246, 251),
      rgb(250, 246, 248),
      white
    );
  }
  .app-title-bar {
    height: 36px;
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mac-title-bar {
    padding-left: 80px;
  }

  .app-menu {
    padding-top: 7px;
    width: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .app-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    border-top-left-radius: 8px;
    background-color: #fff;
    border-top: 1px solid rgb(239, 239, 239);
    border-left: 1px solid rgb(239, 239, 239);
  }

  .app-root {
    display: flex;
    height: 100%;
    position: relative;
  }
  .button-caption,
  .close-button-caption {
    -webkit-app-region: no-drag;
    height: 36px;
    width: 46px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .button-caption {
    background-color: var(--light-button-background-normal-color);
    color: var(--light-button-icon-normal-color);
  }

  .button-caption:hover {
    background-color: var(--light-button-background-hovered-color);
    color: var(--light-button-icon-hovered-color);
  }

  .button-caption:active {
    background-color: var(--light-button-background-pressed-color);
    color: var(--light-button-icon-pressed-color);
  }

  .button-caption:disabled {
    color: var(--light-button-icon-disabled-color);
    pointer-events: none;
  }

  /* Close button in light mode */
  .close-button-caption {
    background-color: var(--light-close-button-background-normal-color);
    color: var(--light-close-button-icon-normal-color);
  }

  .close-button-caption:hover {
    background-color: var(--light-close-button-background-hovered-color);
    color: var(--light-close-button-icon-hovered-color);
  }

  .close-button-caption:active {
    background-color: var(--light-close-button-background-pressed-color);
    color: var(--light-close-button-icon-pressed-color);
  }

  .close-button-caption:disabled {
    color: var(--light-close-button-icon-disabled-color);
  }
}
.overlay-div {
  opacity: 1;
  left: 20px;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s;
  user-select: none;
  position: absolute;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.address-input:focus + .overlay-div {
  opacity: 0;
}

.transition-property-none {
  transition-property: none;
}

.tab-container {
  position: relative;
  container-type: inline-size;
}

.tab-container:not([data-active='true'])::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: gray;
  opacity: 0.1;
}

.tab-container:not([data-active='true']):has(+ [data-active='true'])::after {
  display: none;
}

@container (max-width: 30px) {
  .tab-text,
  .tab-close {
    display: none !important;
  }

  .tab-icon {
    margin: 0 auto !important;
    margin-left: 0 !important;
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }

  .tab-close-icon {
    margin: 0 auto !important;
    margin-left: 0 !important;
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

@container (min-width: 31px) and (max-width: 120px) {
  .tab-close {
    display: none !important;
  }
}

.Throttle-root {
  pointer-events: auto;
  animation: throttle 1s step-end forwards;
}

.Throttle-root:active {
  animation: none;
}

@keyframes throttle {
  from {
    cursor: not-allowed;
    pointer-events: none;
  }
  to {
    pointer-events: auto;
  }
}
