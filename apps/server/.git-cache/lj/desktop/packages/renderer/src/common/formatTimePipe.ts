export function formatTimePipe(timer: number = new Date().getTime()) {
  const today = new Date()
  const todayDate = {
    y: today.getFullYear(),
    m: today.getMonth() + 1 < 10 ? '0' + (today.getMonth() - -1) : today.getMonth() - -1,
    d: today.getDate() < 10 ? '0' + today.getDate() : today.getDate()
  }
  const todayTimer = Date.parse(todayDate.y + '/' + todayDate.m + '/' + todayDate.d + ' 00:00:00')

  const stamp: number[] = []
  // 一天内
  stamp[0] = todayTimer + 86400000
  // 当前时间
  stamp[1] = todayTimer
  // 一天之前
  stamp[2] = todayTimer - 86400000
  // 2天前
  stamp[3] = todayTimer - 172800000
  // 7天
  stamp[4] = todayTimer - 518400000
  // 今年
  stamp[5] = new Date(`${today.getFullYear()}-01-01`).getTime()

  const compareTimer = new Date()
  compareTimer.setTime(timer)

  let transformString = ''

  const yearText = '年'
  const monthText = '月'
  const dayText = '日'

  let minutes: string | number = compareTimer.getMinutes()
  let hours: string | number = compareTimer.getHours()

  hours = hours < 10 ? '0' + hours : hours
  minutes = minutes < 10 ? '0' + minutes : minutes

  if (timer >= stamp[1] && timer < stamp[0]) {
    transformString = `${hours}:${minutes}`
  } else if (timer >= stamp[2] && timer < stamp[1]) {
    transformString = `昨天 ${hours}:${minutes}`
  } else if (timer >= stamp[4] && timer < stamp[2]) {
    // 7天内
    const dath = compareTimer.getDay()
    const SundayText = '星期天'
    const MondayText = '星期一'
    const TuesdayText = '星期二'
    const WednesdayText = '星期三'
    const ThursdayText = '星期四'
    const FridayText = '星期五'
    const SaturdayText = '星期六'

    let dathText

    switch (dath) {
      case 0:
        dathText = SundayText
        break
      case 1:
        dathText = MondayText
        break
      case 2:
        dathText = TuesdayText
        break
      case 3:
        dathText = WednesdayText
        break
      case 4:
        dathText = ThursdayText
        break
      case 5:
        dathText = FridayText
        break
      case 6:
        dathText = SaturdayText
        break
      default:
        break
    }

    transformString = `${dathText} ${hours}:${minutes}`
  } else if (timer >= stamp[5] && timer < stamp[4]) {
    // 365天内
    transformString = `${(compareTimer.getMonth() - -1)
      .toString()
      .padStart(2, '0')}${monthText}${compareTimer.getDate().toString().padStart(2, '0')}${dayText}`
  } else {
    transformString = `${compareTimer.getFullYear()}${yearText}${(compareTimer.getMonth() - -1)
      .toString()
      .padStart(2, '0')}${monthText}${compareTimer.getDate().toString().padStart(2, '0')}${dayText}`
  }
  return transformString
}
