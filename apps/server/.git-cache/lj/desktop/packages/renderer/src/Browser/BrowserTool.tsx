import { showMoreMenu } from '@renderer/common/bridge'
import { webviewManagerAPI } from '@renderer/common/bridge/webview'
import clsx from 'clsx'
import type { TabInfo } from 'electron-global/webview'
import { ChevronLeft, ChevronRight, EllipsisVertical, Globe, RotateCcw } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { BrowserToolAuth } from './BrowserToolAuth'

export function BrowserTool({ activeTab }: { activeTab: TabInfo | null }) {
  // const [isBookmarked, setIsBookmarked] = useState(false)
  const [isForceInput, setIsForceInput] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const isForce = useRef(false)

  const [addressBarValue, setAddressBarValue] = useState({
    name: '',
    url: ''
  })

  useEffect(() => {
    if (activeTab) {
      setAddressBarValue({
        name: activeTab.title,
        url: activeTab.isNewTabPage ? '' : activeTab.url
      })
      setIsForceInput(false)
    }
  }, [activeTab])

  const handleAddressBarSubmit = (e: React.FormEvent | React.KeyboardEvent) => {
    e.preventDefault()
    if (activeTab && addressBarValue.url.trim()) {
      void webviewManagerAPI.searchOrNavigate(addressBarValue.url.trim(), activeTab.id)
    }
  }

  return (
    <div className="flex flex-1 flex-col">
      {/* 导航栏 */}
      <div className="flex h-[44px] items-center gap-2 border-b border-gray-200 bg-gray-50 px-2">
        {/* 导航按钮组 */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => webviewManagerAPI.goBack(activeTab?.id)}
            disabled={!activeTab?.canGoBack || activeTab?.isHome}
            className={clsx(
              'app-no-region rounded p-2 transition-colors hover:bg-gray-100 disabled:opacity-50',
              {
                'pointer-events-none': !activeTab?.canGoBack
              }
            )}
            title="后退"
          >
            <ChevronLeft size={16} />
          </button>
          <button
            onClick={() => webviewManagerAPI.goForward(activeTab?.id)}
            disabled={!activeTab?.canGoForward || activeTab?.isHome}
            className={clsx(
              'app-no-region rounded p-2 transition-colors hover:bg-gray-100 disabled:opacity-50',
              {
                'pointer-events-none': !activeTab?.canGoBack
              }
            )}
            title="前进"
          >
            <ChevronRight size={16} />
          </button>
          <button
            data-tab-id={activeTab?.id}
            data-reload
            onClick={() => webviewManagerAPI.reload(activeTab?.id)}
            className="app-no-region -scale-x-100 rounded p-2 transition-colors hover:bg-gray-100"
            title="刷新"
          >
            <RotateCcw size={16} className="pointer-events-none" />
          </button>
        </div>

        {/* 地址栏 */}
        <div className="flex flex-1 items-center">
          <div className="relative flex-1 rounded-full bg-gray-100">
            <div className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400">
              <Globe size={14} />
            </div>

            <input
              ref={inputRef}
              type="text"
              disabled={activeTab?.isAuth || activeTab?.isUpdateAuth || !isForceInput}
              value={addressBarValue.url ?? ''}
              onChange={(e) => {
                setAddressBarValue((prev) => ({ ...prev, url: e.target.value }))
              }}
              onMouseUp={(e) => {
                if (isForce.current) {
                  e.preventDefault()
                  isForce.current = false
                }
              }}
              onBlur={() => {
                setIsForceInput(false)
              }}
              onFocus={(e) => {
                e.currentTarget.select()
                isForce.current = true
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddressBarSubmit(e)
                }
              }}
              className={`address-input app-no-region w-full rounded-full py-1.5 pr-4 pl-10 text-sm opacity-0 transition-all focus:text-gray-700 focus:opacity-100 focus:ring-2 focus:ring-blue-500`}
              placeholder="搜索或输入网址"
            />

            {!isForceInput && (
              <div
                className="overlay-div h-[30px] text-gray-600"
                onClick={() => {
                  if (activeTab?.isAuth || activeTab?.isUpdateAuth) {
                    return
                  }

                  setIsForceInput(true)
                  void Promise.resolve().then(() => {
                    inputRef.current?.focus()
                  })
                }}
              >
                {!activeTab?.isNewTabPage ? activeTab?.domain || activeTab?.title : activeTab.title}
              </div>
            )}
            {activeTab?.isLoading && (
              <div className="absolute top-1/2 right-3 -translate-y-1/2 transform">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧工具栏 */}
        <div className="flex items-center gap-1">
          {/* <button
            onClick={() => setIsBookmarked(!isBookmarked)}
            className={`app-no-region rounded p-2 transition-colors hover:bg-gray-100 ${
              isBookmarked ? 'text-yellow-500' : 'text-gray-600'
            }`}
            title="收藏"
          >
            <Star size={16} fill={isBookmarked ? 'currentColor' : 'none'} />
          </button> */}
          <button
            onClick={showMoreMenu}
            className="app-no-region rounded p-2 transition-colors hover:bg-gray-100"
            title="更多"
          >
            <EllipsisVertical size={16} />
          </button>
          {(activeTab?.isAuth || activeTab?.isUpdateAuth) && (
            <BrowserToolAuth isUpdate={activeTab.isUpdateAuth} isLoading={activeTab.isLoading} />
          )}
        </div>
      </div>
    </div>
  )
}
