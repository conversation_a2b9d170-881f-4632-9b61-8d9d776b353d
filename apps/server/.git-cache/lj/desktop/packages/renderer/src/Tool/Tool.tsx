import { Button } from '@renderer/components/Button'
import ToolImage from '../assets/tool.png'

export function Tool() {
  return (
    <div className="relative flex h-full flex-col justify-between p-3">
      <div>
        <p className="text-lg">如何完成账号授权</p>
        <p className="mt-1 text-sm">登录页面跳转成功后, 点击"我已完成登录"完成账号授权</p>
      </div>

      <img src={ToolImage} alt="tool-image" />

      <div className="ml-auto flex h-[32px] gap-2">
        <Button
          outlined
          onClick={() => {
            window.onClose?.(0)
          }}
        >
          我知道了
        </Button>
      </div>
    </div>
  )
}
