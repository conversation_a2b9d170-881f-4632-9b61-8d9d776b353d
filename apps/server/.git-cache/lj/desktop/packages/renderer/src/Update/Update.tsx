import { useAutoUpdater } from '@renderer/common/hooks/useAutoUpdater'

export function Update() {
  const {
    currentEvent,
    isChecking,
    isDownloading,
    hasUpdate,
    hasError,
    isUpdateReady,
    canInstall,
    downloadProgress,
    updateInfo,
    error,
    autoDownload,
    description,
    actions
  } = useAutoUpdater()

  console.table({
    currentEvent,
    isChecking,
    isDownloading,
    hasUpdate,
    hasError,
    isUpdateReady,
    canInstall,
    downloadProgress,
    updateInfo,
    error,
    autoDownload,
    actions
  })

  // 获取状态描述
  const getStatusDescription = () => {
    if (hasError) return '更新过程中出现错误，请检查网络连接或稍后重试'
    if (isUpdateReady) return '更新包已下载完成，点击重启应用以完成更新'
    if (isDownloading) return '正在从服务器下载最新版本，请耐心等待'
    if (hasUpdate && !autoDownload) return '发现新版本，点击下载按钮开始更新'
    if (hasUpdate && autoDownload) return '发现新版本，正在自动下载更新包'
    if (isChecking) return '正在连接服务器检查是否有新版本可用'
    return '您当前使用的是最新版本，无需更新'
  }

  // 获取进度条颜色
  const getProgressColor = () => {
    if (downloadProgress === 100) return 'bg-[#714ef9]'
    if (isDownloading) return 'bg-[#714ef9]'
    return 'bg-gray-300'
  }

  return (
    <div className="h-full p-4">
      <div className="mx-auto flex h-full max-w-2xl flex-col">
        {/* 页面标题 */}

        <h1 className="pb-2 text-center text-2xl font-bold text-gray-800 select-none">应用更新</h1>

        {/* 主要更新卡片 */}
        <div className="flex flex-1 flex-col gap-3 overflow-hidden rounded-xl">
          {/* 状态显示 */}

          <p className="text-center leading-relaxed text-gray-600 select-none">
            {getStatusDescription()}
          </p>

          {/* 版本信息 */}
          {updateInfo && !hasError && (
            <div className="flex flex-1 flex-col overflow-hidden">
              <h3 className="p-4 pb-0 font-semibold">🎉 新版本详情</h3>
              <div className="space-y-2 p-4">
                <div className="flex justify-between">
                  <span className="font-medium">版本号:</span>
                  <span>{updateInfo.version}</span>
                </div>
                {updateInfo.releaseDate && (
                  <div className="flex justify-between">
                    <span className="font-medium">发布日期:</span>
                    <span>{new Date(updateInfo.releaseDate).toLocaleDateString('zh-CN')}</span>
                  </div>
                )}
              </div>
              <div className="mx-4 h-[1px] bg-blue-100"></div>
              {description && (
                <div className="flex flex-1 flex-col overflow-hidden">
                  <p className="mb-2 p-4 pb-0 font-semibold">🔖 更新说明:</p>
                  <div className="CustomScrollBar-root flex-1 rounded p-4 pt-0 text-sm whitespace-pre-line">
                    {description}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 错误信息 */}
          {hasError && error && (
            <div className="flex w-full flex-1 flex-col items-start overflow-hidden rounded-lg border border-red-100 bg-red-50">
              <h3 className="p-2 font-semibold text-red-800">
                <span className="mr-1 text-xl">⚠️</span>更新出错
              </h3>
              <div className="CustomScrollBar-root w-full flex-1 rounded p-2 pt-0">
                <pre className="font-mono text-sm break-all whitespace-pre-wrap text-red-700">
                  {error.message}
                </pre>
              </div>
            </div>
          )}

          {/* 操作按钮区域 */}
          <div className="mt-auto space-y-3">
            {hasUpdate && !autoDownload && !isDownloading && !isUpdateReady && (
              <button
                onClick={actions.downloadUpdate}
                className="Throttle-root flex w-full items-center justify-center rounded-lg bg-[#714ef9] px-6 py-2 font-medium text-white transition-colors duration-200 hover:bg-[#603af9]"
              >
                下载更新
              </button>
            )}

            {/* 重启并安装按钮 */}
            {canInstall && (
              <button
                onClick={actions.quitAndInstall}
                className="Throttle-root flex w-full items-center justify-center rounded-lg bg-[#714ef9] px-6 py-2 font-medium text-white hover:bg-[#603af9]"
              >
                重启并安装
              </button>
            )}

            {/* 下载中按钮 */}
            {isDownloading && (
              <button
                disabled
                className="relative w-full overflow-hidden rounded-lg bg-gray-200 px-6 py-2 font-medium transition-all duration-200 disabled:cursor-not-allowed"
              >
                <div
                  className={`absolute inset-0 transition-all duration-500 ${getProgressColor()}`}
                  style={{ width: `${downloadProgress}%` }}
                />

                {/* 底层文字  */}
                <span className="relative z-10 w-full font-medium">
                  下载中 ({Math.round(downloadProgress)}%)
                </span>

                {/* 顶层文字 */}
                <span
                  className="absolute top-0 left-0 z-20 flex h-full w-full items-center justify-center font-medium text-white transition-all duration-500"
                  style={{
                    clipPath: `inset(0 ${100 - downloadProgress}% 0 0)`
                  }}
                >
                  下载中 ({Math.round(downloadProgress)}%)
                </span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
