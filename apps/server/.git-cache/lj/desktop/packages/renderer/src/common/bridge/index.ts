import { InstructEnum, IpcChannelEnum, WindowsInstructEnum } from 'electron-global/enum'

export const { ipc<PERSON><PERSON><PERSON>, preloadUtils } = window.app

export function updateVersion(data: {
  forceUpdate: boolean
  modifyContent: string
  url: string
  versionName: string
}) {
  return ipcRenderer.sendMessage({ type: InstructEnum.UpdateVersion, data })
}

/**
 * 获取系统权限
 * @returns
 */
export function getAskForMediaAccess(): Promise<boolean> {
  return ipcRenderer.sendMessage({ type: InstructEnum.SystemPreferences })
}

/**
 * 更新菜单
 * @returns
 */
export function updateTrayMenu() {
  return ipcRenderer.sendMessage({ type: InstructEnum.UpdateAppMenu })
}
/**
 * 保存登录信息
 * @param userInfo
 * @returns
 */
export function saveUserInfo(userInfo: AnyObject) {
  return ipcRenderer.sendMessage({ type: InstructEnum.SaveUserInfo, data: userInfo })
}

/**
 * 创建新用户登录
 * @returns
 */
export function createMainWindow() {
  return ipcRenderer.sendMessage({ type: InstructEnum.CreateMainWindow })
}

/**
 * 注册显示窗口快捷键
 * @param data
 * @returns
 */
export function registrationShortcutDisplayWindow(data: { oldShort: string; newShort: string }) {
  return ipcRenderer.sendMessage({ type: InstructEnum.RegistrationShortcutDisplayWindow, data })
}

/**
 * 保存文件
 * @param data
 * @returns
 */
export function saveAsFile(data: { fileName: string; localUrl: string }): Promise<boolean> {
  return ipcRenderer.sendMessage({ type: InstructEnum.SaveAsFile, data })
}

export function windowsTrafficLights() {
  return {
    maximize: () => ipcRenderer.sendMessage({ type: WindowsInstructEnum.WindowMax }),
    minimize: () => ipcRenderer.sendMessage({ type: WindowsInstructEnum.WindowMin }),
    close: () => ipcRenderer.sendMessage({ type: WindowsInstructEnum.WindowClose })
  }
}

/**
 * 发送通知
 * @param data
 * @returns
 */
export function sendNotice(data: AnyObject) {
  return ipcRenderer.invoke(IpcChannelEnum.Notice, data)
}

/**
 * 发送通知 macos
 * @param sessionId
 * @returns
 */
export function sendMacosNotification(sessionId: string) {
  return ipcRenderer.sendMessage({ type: InstructEnum.MacosSendNotification, data: sessionId })
}

/**
 * 检查是否聚焦
 * @returns
 */
export function checkFocus() {
  return ipcRenderer.sendMessage({ type: InstructEnum.isFocus })
}

/**
 * 发送原生弹窗
 * @param data
 * @returns
 */
export function openNativeDialog(
  data: Electron.MessageBoxOptions
): Promise<Electron.MessageBoxReturnValue> {
  return ipcRenderer.sendMessage({ type: InstructEnum.NativeDialog, data })
}

/**
 * 强制退出
 * @returns
 */
export function forceQuit() {
  return ipcRenderer.sendMessage({ type: InstructEnum.Quit })
}

/**
 * 设置主题
 * @param data
 * @returns
 */
export function setWindowTheme(data: string) {
  return ipcRenderer.sendMessage({ type: InstructEnum.SetTheme, data: { color: data } })
}

/**
 * 登录
 * @returns
 */
export function login() {
  return ipcRenderer.sendMessage({ type: InstructEnum.Login })
}

/**
 * 隐藏
 * @returns
 */
export function hideWindow() {
  return ipcRenderer.sendMessage({ type: InstructEnum.Hide })
}

export function showMoreMenu() {
  return ipcRenderer.sendMessage({ type: InstructEnum.ShowMoreMenu })
}

export async function getToolPagePath() {
  return ipcRenderer.sendMessage<string>({ type: InstructEnum.GetToolPath })
}

export function checkUpdate() {
  return ipcRenderer.sendMessage({ type: InstructEnum.CheckUpdate })
}

export function getSocketSeverState() {
  return ipcRenderer.invoke('serverSocket:getState')
}

export function getClientState() {
  return ipcRenderer.invoke('client:getState')
}
