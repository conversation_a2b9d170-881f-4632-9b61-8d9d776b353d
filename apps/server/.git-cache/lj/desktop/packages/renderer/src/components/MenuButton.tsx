import clsx from 'clsx'

export function MenuButton({
  active,
  icon,
  className,
  ...other
}: {
  icon: React.ReactNode
  active: boolean
} & React.HTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      data-true={active}
      className={clsx(
        'flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-lg hover:bg-gray-300',
        className,
        {
          'bg-gray-300': active
        }
      )}
      {...other}
    >
      {icon}
    </button>
  )
}
