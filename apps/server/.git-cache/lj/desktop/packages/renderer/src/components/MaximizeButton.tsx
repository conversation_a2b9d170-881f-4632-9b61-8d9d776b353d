export function MaximizeButton({
  isMaximized,
  ...others
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { isMaximized: boolean }) {
  return (
    <button className="button-caption" {...others} onContextMenu={(e) => e.preventDefault()}>
      {isMaximized ? (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" height={16}>
          <path
            d="M11.999,5.96387 C11.999,5.69368 11.9453,5.43978 11.8379,5.20215 C11.7305,4.96126 11.584,4.75293 11.3984,4.57715 C11.2161,4.39811 11.0029,4.25814 10.7588,4.15723 C10.5179,4.05306 10.264,4.00098 9.99707,4.00098 L5.08496,4.00098 C5.13704,3.85124 5.21029,3.71452 5.30469,3.59082 C5.39909,3.46712 5.50814,3.36133 5.63184,3.27344 C5.75553,3.18555 5.89062,3.11882 6.03711,3.07324 C6.18685,3.02441 6.34147,3 6.50098,3 L9.99707,3 C10.4105,3 10.7995,3.07975 11.1641,3.23926 C11.5286,3.39551 11.846,3.60872 12.1162,3.87891 C12.3896,4.14909 12.6045,4.46647 12.7607,4.83105 C12.9202,5.19564 13,5.58464 13,5.99805 L13,9.49902 C13,9.65853 12.9756,9.81315 12.9268,9.96289 C12.8812,10.1094 12.8145,10.2445 12.7266,10.3682 C12.6387,10.4919 12.5329,10.6009 12.4092,10.6953 C12.2855,10.7897 12.1488,10.863 11.999,10.915 L11.999,5.96387 Z M4.47461,13 C4.2793,13 4.09212,12.9609 3.91309,12.8828 C3.73405,12.8014 3.57617,12.694 3.43945,12.5605 C3.30599,12.4238 3.19857,12.266 3.11719,12.0869 C3.03906,11.9079 3,11.7207 3,11.5254 L3,6.47656 C3,6.27799 3.03906,6.09082 3.11719,5.91504 C3.19857,5.736 3.30599,5.57975 3.43945,5.44629 C3.57617,5.30957 3.73242,5.20215 3.9082,5.12402 C4.08724,5.04264 4.27604,5.00195 4.47461,5.00195 L9.52344,5.00195 C9.72201,5.00195 9.91081,5.04264 10.0898,5.12402 C10.2689,5.20215 10.4251,5.30794 10.5586,5.44141 C10.6921,5.57487 10.7979,5.73112 10.876,5.91016 C10.9574,6.08919 10.998,6.27799 10.998,6.47656 L10.998,11.5254 C10.998,11.724 10.9574,11.9128 10.876,12.0918 C10.7979,12.2676 10.6904,12.4238 10.5537,12.5605 C10.4202,12.694 10.264,12.8014 10.085,12.8828 C9.90918,12.9609 9.72201,13 9.52344,13 L4.47461,13 Z M9.49902,11.999 C9.56738,11.999 9.63086,11.986 9.68945,11.96 C9.7513,11.9339 9.80501,11.8981 9.85059,11.8525 C9.89941,11.807 9.93685,11.7549 9.96289,11.6963 C9.98893,11.6344 10.002,11.5693 10.002,11.501 L10.002,6.50098 C10.002,6.43262 9.98893,6.36751 9.96289,6.30566 C9.93685,6.24382 9.90104,6.1901 9.85547,6.14453 C9.8099,6.09896 9.75618,6.06315 9.69434,6.03711 C9.63249,6.01107 9.56738,5.99805 9.49902,5.99805 L4.49902,5.99805 C4.43066,5.99805 4.36556,6.01107 4.30371,6.03711 C4.24512,6.06315 4.19303,6.10059 4.14746,6.14941 C4.10189,6.19499 4.06608,6.2487 4.04004,6.31055 C4.014,6.36914 4.00098,6.43262 4.00098,6.50098 L4.00098,11.501 C4.00098,11.5693 4.014,11.6344 4.04004,11.6963 C4.06608,11.7549 4.10189,11.807 4.14746,11.8525 C4.19303,11.8981 4.24512,11.9339 4.30371,11.96 C4.36556,11.986 4.43066,11.999 4.49902,11.999 L9.49902,11.999 Z"
            fill="currentColor"
          />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" height={16}>
          <path
            d="M4.47461,13 C4.2793,13 4.09212,12.9609 3.91309,12.8828 C3.73405,12.8014 3.57617,12.694 3.43945,12.5605 C3.30599,12.4238 3.19857,12.266 3.11719,12.0869 C3.03906,11.9079 3,11.7207 3,11.5254 L3,4.47461 C3,4.2793 3.03906,4.09212 3.11719,3.91309 C3.19857,3.73405 3.30599,3.5778 3.43945,3.44434 C3.57617,3.30762 3.73405,3.2002 3.91309,3.12207 C4.09212,3.04069 4.2793,3 4.47461,3 L11.5254,3 C11.7207,3 11.9079,3.04069 12.0869,3.12207 C12.266,3.2002 12.4222,3.30762 12.5557,3.44434 C12.6924,3.5778 12.7998,3.73405 12.8779,3.91309 C12.9593,4.09212 13,4.2793 13,4.47461 L13,11.5254 C13,11.7207 12.9593,11.9079 12.8779,12.0869 C12.7998,12.266 12.6924,12.4238 12.5557,12.5605 C12.4222,12.694 12.266,12.8014 12.0869,12.8828 C11.9079,12.9609 11.7207,13 11.5254,13 L4.47461,13 M11.501,11.999 C11.5693,11.999 11.6328,11.986 11.6914,11.96 C11.7533,11.9339 11.807,11.8981 11.8525,11.8525 C11.8981,11.807 11.9339,11.7549 11.96,11.6963 C11.986,11.6344 11.999,11.5693 11.999,11.501 L11.999,4.49902 C11.999,4.43066 11.986,4.36719 11.96,4.30859 C11.9339,4.24674 11.8981,4.19303 11.8525,4.14746 C11.807,4.10189 11.7533,4.06608 11.6914,4.04004 C11.6328,4.014 11.5693,4.00098 11.501,4.00098 L4.49902,4.00098 C4.43066,4.00098 4.36556,4.014 4.30371,4.04004 C4.24512,4.06608 4.19303,4.10189 4.14746,4.14746 C4.10189,4.19303 4.06608,4.24674 4.04004,4.30859 C4.014,4.36719 4.00098,4.43066 4.00098,4.49902 L4.00098,11.501 C4.00098,11.5693 4.014,11.6344 4.04004,11.6963 C4.06608,11.7549 4.10189,11.807 4.14746,11.8525 C4.19303,11.8981 4.24512,11.9339 4.30371,11.96 C4.36556,11.986 4.43066,11.999 4.49902,11.999 L11.501,11.999 Z"
            fill="currentColor"
          />
        </svg>
      )}
    </button>
  )
}
