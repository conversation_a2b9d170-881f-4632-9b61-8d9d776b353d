import path from 'path'
import { defineConfig } from 'vite'

import { node } from '../../.electron-vendors.cache.json'
import { version } from '../../package.json'

const PackageRoot = __dirname

/**
 * @see https://vitejs.dev/config/
 */
const config = defineConfig({
  mode: process.env.MODE,
  root: PackageRoot,
  envDir: process.cwd(),
  publicDir: path.join(PackageRoot, 'assets'),
  define: {
    __APP_VERSION__: JSON.stringify(version),
    __API_URL: JSON.stringify(process.env.VITE_API_URL),
    __IS_PROD__: `${process.env.NODE_ENV === 'production'}`
  },
  resolve: {
    alias: {
      'electron-global': path.join(PackageRoot, '..', 'global')
    }
  },
  build: {
    ssr: true,
    target: `node${node}`,
    outDir: 'dist',
    assetsDir: path.join(PackageRoot, 'assets'),
    minify: false,
    lib: {
      entry: 'src/index.ts',
      formats: ['cjs']
    },
    rollupOptions: {
      output: {
        entryFileNames: '[name].cjs'
      }
    },
    emptyOutDir: true,
    reportCompressedSize: false
  }
})

export default config
