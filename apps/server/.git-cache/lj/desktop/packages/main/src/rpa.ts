import { ipcMain } from 'electron'
import { AppContext } from './context'
import vm from 'vm'
import { appCachePath } from 'electron-global/appCachePath'
import { injection } from '@yixiaoer/platform-service'
import mime from 'mime'
import type { ImageTask, VideoTask } from '../../../rpa/src/type'
import { getStore } from './utils'
import { task } from './task'
import axios from 'axios'
import fs from 'fs'
import path from 'path'
import { showWindow } from './menu'
import { Logger } from './logger'

const logger = Logger.getLogger('rpa')

const existsSync = (path: string) => {
  return fs.existsSync(path)
}

export const rpaChannel = {
  start: 'rpa-start',
  sendEvent: 'rpa-send-event',
  sendEntryEvent: 'rpa-send-entry-event',
  publishBulk: 'publish-bulk-event',
  sendPublishBulk: 'send-publish-bulk'
}

ipcMain.handle('read-file-buffer', (_event, filePath: string) => {
  return fs.readFileSync(filePath)
})

ipcMain.on(rpaChannel.publishBulk, async (event, tabId: string) => {
  const view = AppContext.getInstance().getWebviewManager().getView(tabId)

  let returnValue = false
  if (view) {
    try {
      returnValue = await view.webContents.executeJavaScript('startPush()')
      console.log('returnValue', returnValue)
    } catch (error) {
      //
      console.log(error)
    }
  }
  event.returnValue = returnValue
})

ipcMain.handle(rpaChannel.sendEntryEvent, (_event, tabId: string) => {
  const view = AppContext.getInstance().getWebviewManager().getView(tabId)

  if (view) {
    view.webContents.sendInputEvent({
      type: 'keyDown',
      keyCode: 'Enter',
      modifiers: []
    })

    view.webContents.sendInputEvent({
      type: 'keyUp',
      keyCode: 'Enter'
    })
  }
})

ipcMain.handle(rpaChannel.sendEvent, (_event, value: string, tabId: string) => {
  const view = AppContext.getInstance().getWebviewManager().getView(tabId)

  if (view) {
    const chars = [...value]
    view.webContents.focus()

    for (const char of chars) {
      view.webContents.sendInputEvent({
        type: 'keyDown',
        keyCode: char,
        modifiers: []
      })

      if (char !== ' ') {
        view.webContents.sendInputEvent({
          type: 'char',
          keyCode: char
        })
      } else {
        view.webContents.sendInputEvent({
          type: 'char',
          keyCode: 'Space'
        })
      }

      view.webContents.sendInputEvent({
        type: 'keyUp',
        keyCode: char
      })
    }
  }
})

export async function rpaVideoStart(
  data: { video: string; cover: string },
  config: VideoTask & {
    cookies: Electron.Cookie[]
    localStorage: AnyObject
  }
) {
  showWindow(AppContext.getInstance().getMainWindow())

  const code = import.meta.env.DEV
    ? fs.readFileSync(path.join(process.cwd(), 'rpa', 'dist', 'index.js')).toString()
    : (getStore('rapData') as string)

  if (!code) {
    return
  }

  const platform = task.getPlatformService().injection.platformConfig[config.platformName as string]
  if (!platform) {
    return
  }

  let videoFilePath = data.video
  let coverFilePath = data.cover

  if (!data.video || !data.cover) {
    return
  }

  if (data.video.startsWith('https://') || data.video.startsWith('http://')) {
    const videoFile = await axios.get(data.video, {
      responseType: 'arraybuffer'
    })
    videoFilePath = path.join(appCachePath, 'rpa', path.basename(data.video))
    fs.writeFileSync(videoFilePath, videoFile.data)
  }

  if (data.cover.startsWith('https://') || data.cover.startsWith('http://')) {
    const coverFile = await axios.get(data.cover, {
      responseType: 'arraybuffer'
    })

    coverFilePath = path.join(appCachePath, 'rpa', path.basename(data.cover))

    fs.writeFileSync(coverFilePath, coverFile.data)
  }

  const webviewManager = AppContext.getInstance().getWebviewManager()

  const newConfigs = [
    {
      ...config,
      videoPath: videoFilePath,
      videoCover: coverFilePath,
      videoName: path.basename(videoFilePath),
      videoMime: mime.getType(videoFilePath)
    }
  ]

  const getView = (tabId: string) => {
    return webviewManager.getView(tabId)
  }

  const isActiveTab = (tabId: string) => {
    return webviewManager.getActiveTabId() === tabId
  }

  const openUrl = async (
    config: VideoTask & {
      cookies: Electron.Cookie[]
      localStorage: AnyObject
    },
    url: string
  ) => {
    const tabId = await webviewManager.createRestoreAuthTabPageByRpa(
      url,
      config.cookies,
      config.localStorage,
      config.accountId,
      platform.authorizeUrl
    )

    return tabId
  }

  try {
    vm.runInThisContext(
      `((type, startConfigs, platformNames, getView, isActiveTab,openUrl,existsSync, require, exports)=>{${code}})`
    )(
      'video',
      newConfigs,
      injection.platformNames,
      getView,
      isActiveTab,
      openUrl,
      existsSync,
      require,
      exports
    )
  } catch (e) {
    logger.error('Error in VM:', e)
  }
}

export async function rpaImageStart(
  config: Omit<ImageTask, 'images'> & {
    images: string[]
    cookies: Electron.Cookie[]
    localStorage: AnyObject
  }
) {
  showWindow(AppContext.getInstance().getMainWindow())
  const { images, ...rest } = config
  const newConfig: ImageTask & {
    cookies: Electron.Cookie[]
    localStorage: AnyObject
  } = {
    ...rest,
    images: []
  }

  const code = import.meta.env.DEV
    ? fs.readFileSync(path.join(process.cwd(), 'rpa', 'dist', 'index.js')).toString()
    : (getStore('rapData') as string)

  if (!code) {
    return
  }

  let coverFilePath = ''

  if (!images.length) {
    return
  }

  const platform = task.getPlatformService().injection.platformConfig[config.platformName as string]
  if (!platform) {
    return
  }

  for (let i = 0; i < images.length; i++) {
    let imageObject = images[i]

    if (imageObject.startsWith('https://') || imageObject.startsWith('http://')) {
      const videoFile = await axios.get(imageObject, {
        responseType: 'arraybuffer'
      })
      imageObject = path.join(appCachePath, 'rpa', path.basename(imageObject))
      fs.writeFileSync(imageObject, videoFile.data)
    }

    newConfig.images[i] = {
      path: imageObject,
      format: mime.getType(imageObject)?.split('/')[1] || ''
    }
  }

  if (config.cover.startsWith('https://') || config.cover.startsWith('http://')) {
    const coverFile = await axios.get(config.cover, {
      responseType: 'arraybuffer'
    })

    coverFilePath = path.join(appCachePath, 'rpa', path.basename(config.cover))

    fs.writeFileSync(coverFilePath, coverFile.data)
  }

  const webviewManager = AppContext.getInstance().getWebviewManager()

  const getView = (tabId: string) => {
    return webviewManager.getView(tabId)
  }

  const isActiveTab = (tabId: string) => {
    return webviewManager.getActiveTabId() === tabId
  }

  const openUrl = async (
    config: VideoTask & {
      cookies: Electron.Cookie[]
      localStorage: AnyObject
    },
    url: string
  ) => {
    const tabId = await webviewManager.createRestoreAuthTabPageByRpa(
      url,
      config.cookies,
      config.localStorage,
      config.accountId,
      platform.authorizeUrl
    )

    return tabId
  }

  try {
    vm.runInThisContext(
      `((type, startConfigs, platformNames, getView, isActiveTab,openUrl,existsSync, require, exports)=>{${code}})`
    )(
      'image-text',
      [newConfig],
      injection.platformNames,
      getView,
      isActiveTab,
      openUrl,
      existsSync,
      require,
      exports
    )
  } catch (e) {
    logger.error('Error in VM:', e)
  }
}
