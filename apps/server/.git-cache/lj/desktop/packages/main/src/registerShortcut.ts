// import { globalShortcut, type BrowserWindow } from 'electron'
// import { AppContext } from './context'

import type { BrowserWindow } from 'electron'

export function registerShortcut(mainWindow: BrowserWindow) {
  // globalShortcut.register('CommandOrControl+W', () => {
  //   if (mainWindow.isFocused()) {
  //     AppContext.getInstance().getWebviewManager().closeActiveTab()
  //   }
  // })

  return mainWindow
}
