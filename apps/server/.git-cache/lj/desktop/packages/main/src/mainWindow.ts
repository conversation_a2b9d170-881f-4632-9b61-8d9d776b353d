import { getAssetPath, getPreloadPath, MainPageSize, titleBarStyle } from './constant'
import { createWindow } from './createWindow'
import { updateTrayMenu } from './menu'
import { mainPagePath } from './pagePath'
import { WebviewManager } from './webview'
import { AppContext } from './context'
import { UpdaterManager } from './update'
import { getStore, setStore } from './utils'
import { nanoid } from 'nanoid'
import { Menu } from 'electron'
import { isWin } from 'electron-global/platform'

export async function createMainWindow() {
  const preloadPath = getPreloadPath()
  const appWindow = await createWindow({
    path: mainPagePath,
    ...MainPageSize,
    ...titleBarStyle,
    isHideReplaceClose: true,
    resizable: true,
    maximizable: true,
    backgroundColor: '#ffffff',
    show: false,
    icon: getAssetPath('icon.png'),
    webPreferences: {
      spellcheck: false,
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: false,
      webviewTag: false,
      preload: preloadPath
    },
    closeCallback() {
      AppContext.getInstance().getWebviewManager().closeAll()
    }
  })

  appWindow.webContents.on('did-create-window', (childWindow) => {
    childWindow.once('ready-to-show', () => {
      childWindow.show()
    })
  })

  appWindow.webContents.setWindowOpenHandler(function (details) {
    /**
     * @example
     * features=backgroundColor=#000000,trafficLightPosition={x:5,y:5},width=400,height=400
     * -> { backgroundColor: '#000000', trafficLightPosition: { x: 5, y: 5 }, width: 400, height: 400 }
     */
    const features = details.features.split(',').reduce(
      (memo, current) => {
        const [key, value] = current.split('=')
        const isNumber = !isNaN(parseInt(value, 10))
        memo[key] = isNumber ? parseInt(value, 10) : value
        return memo
      },
      {} as Record<string, Placeholder>
    )

    return {
      action: 'allow',
      overrideBrowserWindowOptions: {
        ...features,
        resizable: false,
        maximizable: false,
        parent: appWindow,
        modal: isWin,
        autoHideMenuBar: true,
        accentColor: '#714ef9',
        show: false,
        frame: false,
        fullscreenable: false
      }
    }
  })

  appWindow.webContents.on('context-menu', async (_event, params) => {
    const { tabId, isReload } = await appWindow.webContents.executeJavaScript(`
    (function() {
      const element = document.elementFromPoint(${params.x}, ${params.y})
      if (!element) return {}
      return {
        tabId: element.getAttribute('data-tab-id'),
        isReload: element.getAttribute('data-reload') === 'true'
      }
    })()
  `)

    const menuItems = []
    const webviewManager = AppContext.getInstance().getWebviewManager()

    if (tabId && isReload) {
      if (webviewManager.getView(tabId)?.webContents.isDevToolsOpened()) {
        menuItems.push({
          label: '忽略缓存并重新加载',
          click: () => webviewManager.reload(tabId, true)
        })
      }
    } else if (tabId) {
      if (tabId === 'tab-1') {
        if (webviewManager.getTabSize() > 1) {
          menuItems.push({
            label: '关闭其它标签',
            click: () => webviewManager.closeOtherTabs(tabId)
          })
        }
      } else {
        menuItems.push({
          label: '关闭',
          click: () => webviewManager.closeTab(tabId)
        })

        if (webviewManager.getTabSize() > 2) {
          menuItems.push({
            label: '关闭其它标签',
            click: () => webviewManager.closeOtherTabs(tabId)
          })
        }
      }
    }

    if (menuItems.length > 0) {
      const menu = Menu.buildFromTemplate(menuItems)
      menu.popup({ x: params.x, y: params.y })
    }
  })

  let deviceId: string = getStore('deviceId')

  if (!deviceId) {
    deviceId = nanoid()
    setStore('deviceId', deviceId)
  }

  AppContext.getInstance().setMainWindow(appWindow)
  AppContext.getInstance().setUpdaterManager(new UpdaterManager(appWindow))
  AppContext.getInstance().setWebviewManager(new WebviewManager(appWindow, preloadPath))

  appWindow.webContents.send('init-webview-manager')

  appWindow.focus()

  updateTrayMenu()

  return appWindow
}
