import { BrowserWindow, ipcMain } from 'electron'
import { nativeTheme } from 'electron'
import { updatePagePath } from './pagePath'
import { createContextMenu } from './contextMenu'
import { getPreloadPath, titleBarStyle } from './constant'
import { isWin } from 'electron-global/platform'

export function createUpdateWindow(
  rootWindow: BrowserWindow,
  { webPreferences, ...rest }: Electron.BrowserWindowConstructorOptions = {}
): Promise<BrowserWindow> {
  nativeTheme.themeSource = 'light'
  const preloadPath = getPreloadPath()
  const browserWindow = new BrowserWindow({
    modal: isWin,
    parent: rootWindow,
    resizable: false,
    minimizable: false,
    maximizable: false,
    width: 340,
    height: 423,
    trafficLightPosition: {
      x: 10,
      y: 10
    },
    ...titleBarStyle,
    titleBarOverlay: isWin ? { color: '#ffffff' } : true,
    autoHideMenuBar: true,
    skipTaskbar: true,
    accentColor: '#714ef9',
    show: false,
    webPreferences: {
      spellcheck: false,
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: false,
      webviewTag: false,
      preload: preloadPath,
      ...webPreferences
    },
    ...rest
  })

  createContextMenu({
    window: browserWindow
  })

  if (import.meta.env.DEV) {
    browserWindow.webContents.on('context-menu', (event) => {
      event.preventDefault()
    })
  }

  return new Promise((resolve) => {
    ipcMain.handleOnce('update-window-read', () => {
      resolve(browserWindow)

      browserWindow.show()
      browserWindow.webContents.setZoomFactor(1)
    })

    void browserWindow.loadURL(updatePagePath)
  })
}
