import { execSync } from 'child_process'
import { shell } from 'electron'
import { FileProtocol } from 'electron-global/fileProtocol'

/**
 * 打开文件夹
 * @param path
 */
export function openFileDir(dirPath: string) {
  const pathUrl = dirPath.replace(`${FileProtocol}`, '')
  // Mac os 使用 showItemInFolder 非常慢
  if (process.platform === 'darwin') {
    void execSync(`open -R "${pathUrl}"`)
  } else {
    shell.showItemInFolder(pathUrl)
  }
}
