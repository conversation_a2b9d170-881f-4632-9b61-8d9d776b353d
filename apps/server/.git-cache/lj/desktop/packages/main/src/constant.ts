import { app } from 'electron'
import { isWin } from 'electron-global/platform'
import path, { join } from 'path'
import { isWindows11 } from './utils'

export const win11 = isWindows11()

/**
 * 获取 resource path 主要用于应用 icon
 */
export function getAssetPath(...paths: string[]) {
  const resourcePath = app.isPackaged
    ? join(app.getAppPath(), 'packages', 'main', 'dist')
    : join(__dirname, '..', 'assets')

  return join(resourcePath, ...paths)
}

/**
 * preload path
 * @returns
 */
export const getPreloadPath = () =>
  join(app.getAppPath(), 'packages', 'preload', 'dist', 'index.cjs')

export const getNoticePreloadPath = () =>
  join(app.getAppPath(), 'packages', 'main', 'dist', 'noticePreload.cjs')

/**
 * 区别windows titlebar 样式
 */
export const titleBarStyle = isWin
  ? ({
      titleBarStyle: 'hidden'
    } as const)
  : ({ titleBarStyle: 'hiddenInset' } as const)

export const MainPageSize = {
  width: 1250,
  height: 800,
  minWidth: 800,
  minHeight: 520
}

export const LoginPageSize = {
  width: 600,
  height: 420
}

export const onlineScriptsDirPath = path.join(app.getPath('userData'), 'online-srcripts')

export const reptileFilePath = path.join(onlineScriptsDirPath, 'reptile')
