import { exec, spawn } from 'child_process'
import { Logger } from './logger'

const logger = Logger.getLogger('firewall')

class FirewallManager {
  appPath: string
  appName: string

  constructor(appPath: string, appName: string) {
    this.appPath = appPath
    this.appName = appName
  }

  checkCorrectRule() {
    return new Promise((resolve) => {
      exec(`netsh advfirewall firewall show rule name="${this.appName}"`, (error, stdout) => {
        if (error) {
          resolve(false)
          return
        }

        const hasPrivate = stdout.includes('Private')
        const hasPublic = stdout.includes('Public')
        resolve(hasPrivate && hasPublic)
      })
    })
  }

  executeAsAdmin(command: string) {
    return new Promise<void>((resolve, reject) => {
      const child = spawn('runas', ['/user:Administrator', `netsh ${command}`], {
        stdio: 'pipe',
        shell: true
      })

      let output = ''
      let errorOutput = ''

      child.stdout?.on('data', (data) => {
        output += data.toString()
      })

      child.stderr?.on('data', (data) => {
        errorOutput += data.toString()
      })

      child.on('close', (code) => {
        if (code === 0) {
          resolve()
        } else {
          reject(new Error(`执行失败: ${errorOutput || output}`))
        }
      })

      child.on('error', (error) => {
        reject(error)
      })
    })
  }

  executeNetshAsAdmin(command: string) {
    return new Promise<void>((resolve, reject) => {
      exec(`netsh ${command}`, (error) => {
        if (error) {
          logger.log('执行失败:', error)
          // 如果是权限错误，尝试用 runas
          if (error.message.includes('elevation') || error.message.includes('权限')) {
            this.executeAsAdmin(command).then(resolve).catch(reject)
          } else {
            reject(error)
          }
        } else {
          resolve()
        }
      })
    })
  }

  async removeRule() {
    try {
      const command = `advfirewall firewall delete rule name="${this.appName}"`
      await this.executeNetshAsAdmin(command)
    } catch (error) {
      logger.log('删除旧规则失败（可能不存在）:', error)
    }
  }

  async addRule() {
    const command = `advfirewall firewall add rule name="${this.appName}" dir=in action=allow program="${this.appPath}" profile=private,public`
    await this.executeNetshAsAdmin(command)
  }

  async setup() {
    try {
      const isCorrect = await this.checkCorrectRule()

      if (isCorrect) {
        logger.log('防火墙规则已正确配置')
        return true
      }

      logger.log('重新配置防火墙规则...')
      await this.removeRule()
      await this.addRule()

      logger.log('防火墙规则配置成功')
      return true
    } catch (error) {
      logger.error('防火墙配置失败:', error)
      return false
    }
  }
}

export const firewall = new FirewallManager(process.execPath, import.meta.env.VITE_APP_NAME)
