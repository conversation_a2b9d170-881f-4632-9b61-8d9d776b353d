import type { BrowserWindow } from 'electron'
import type { WebviewManager } from './webview'
import type { SocketManager } from './socket'
import type { UpdaterManager } from './update'
import type { ServerSocketManager } from './serverSocket'

export class AppContext {
  static instance: AppContext
  static getInstance() {
    if (AppContext.instance == null) {
      AppContext.instance = new AppContext()
    }
    return AppContext.instance
  }

  private constructor() {}

  private mainWindow: BrowserWindow | null = null

  private windowMap: Map<number, BrowserWindow> = new Map()

  private webviewManager!: WebviewManager

  private socketManager!: SocketManager

  private serverSocketManager!: ServerSocketManager

  private updaterManager!: UpdaterManager

  private port: number | null = null

  public setMainWindow(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow
  }

  public getMainWindow() {
    return this.mainWindow
  }

  public getPort() {
    return this.port
  }

  public setPort(port: number) {
    this.port = port
  }

  public setUpdaterManager(updaterManager: UpdaterManager) {
    this.updaterManager = updaterManager
  }

  public getUpdaterManager() {
    return this.updaterManager
  }

  public getSocketManager() {
    return this.socketManager
  }

  public setSocketManager(socketManager: SocketManager) {
    this.socketManager = socketManager
  }

  public setServerSocketManager(serverSocketManager: ServerSocketManager) {
    this.serverSocketManager = serverSocketManager
  }

  public getServerSocketManager() {
    return this.serverSocketManager
  }

  public getWebviewManager() {
    return this.webviewManager
  }

  public setWebviewManager(webviewManager: WebviewManager) {
    this.webviewManager = webviewManager
  }

  public addWindow(window: BrowserWindow) {
    this.windowMap.set(window.id, window)
  }

  public removeWindow(id: number) {
    this.windowMap.delete(id)
  }

  public getWindow(id?: number) {
    if (id) {
      return this.windowMap.get(id)
    }

    if (this.windowMap.size) {
      return this.windowMap.values().next().value as BrowserWindow
    }
  }
}
