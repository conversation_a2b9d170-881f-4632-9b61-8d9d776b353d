import { BrowserWindow, Menu } from 'electron'
import { nativeTheme } from 'electron'
import { AppContext } from './context'
import { onlinePagePath } from './pagePath'
import { getPreloadPath, titleBarStyle } from './constant'
import { isWin } from 'electron-global/platform'
import { getStore } from './utils'

export async function createOnlineWindow() {
  nativeTheme.themeSource = 'light'

  const mainWindow = AppContext.getInstance().getMainWindow()
  if (!mainWindow) {
    return
  }

  const browserWindow = new BrowserWindow({
    resizable: false,
    maximizable: false,
    minimizable: false,
    width: 340,
    height: 260,
    parent: mainWindow,
    modal: isWin,
    autoHideMenuBar: true,
    trafficLightPosition: {
      x: 10,
      y: 10
    },
    ...titleBarStyle,
    titleBarOverlay: isWin ? { color: '#ffffff' } : true,
    skipTaskbar: true,
    accentColor: '#714ef9',
    show: false,
    webPreferences: {
      preload: getPreloadPath()
    }
  })

  if (import.meta.env.DEV) {
    const menu = Menu.buildFromTemplate([
      {
        label: '打开开发者工具',
        click() {
          browserWindow.webContents.toggleDevTools()
        }
      }
    ])

    browserWindow.webContents.on('context-menu', (event, params) => {
      event.preventDefault()
      menu.popup({ x: params.x, y: params.y })
    })
  }

  browserWindow.webContents.on('render-process-gone', (_) => {
    browserWindow.reload()
  })

  browserWindow.once('ready-to-show', () => {
    browserWindow.show()
  })

  /**
   * 主窗口的网址。
   * 用于开发的 Vite 开发服务器。
   * `file://../renderer/index.html` 用于生产和测试的。
   */
  await browserWindow.loadURL(`${onlinePagePath}?deviceId=${getStore('deviceId') as string}`)

  return browserWindow
}
