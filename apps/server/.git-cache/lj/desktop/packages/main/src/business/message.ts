import { createAuthContextView, createAuthView, openAuthView, openView } from './auth'

import { AppContext } from '../context'
import { showWindow } from '../menu'

export function dispatchMessages(
  openId: string,
  { type, data }: { type: string; data: AnyObject },
  callback?: (...args: Placeholder[]) => void
) {
  showWindow(AppContext.getInstance().getMainWindow())

  switch (type) {
    case 'collect':
      break
    case 'create-auth-context-view':
      void createAuthContextView({
        url: data.url,
        accountId: data.accountId,
        color: data.color,
        spaceName: data.spaceName,
        unsaved: data.unsaved,
        openId,
        type
      })
      break
    case 'create-auth-view':
      void createAuthView({
        accountId: data.accountId,
        cookies: data.accountSession?.cookies,
        localStorage: data.accountSession?.localStorage,
        platformName: data.platformName,
        openId,
        type
      })

      break
    case 'open-auth-view':
      void openAuthView({
        url: data.url,
        title: data.title,
        platformName: data.platformName,
        cookies: data.cookies,
        localStorage: data.localStorage,
        accountId: data.accountId,
        isUpdateAuth: data.isUpdateAuth,
        openId
      })

      break
    case 'open-view':
      void openView({
        url: data.url,
        color: data.color,
        accountId: data.accountId,
        spaceName: data.spaceName
      })

      break

    case 'publish-rpa':
      break
    default:
      break
  }

  callback?.()
}
