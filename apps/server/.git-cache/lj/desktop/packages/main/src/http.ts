import net from 'net'
import http from 'http'
import fs from 'fs-extra'
import path from 'path'
import { appCachePath } from 'electron-global/appCachePath'

import { Logger } from './logger'
import { getStore } from './utils'
import { AppContext } from './context'
import { task } from './task'
import { rpaImageStart, rpaVideoStart } from './rpa'
import type { ImageTask, VideoTask } from '../../../rpa/src/type'

const logger = Logger.getLogger('http')

function checkPort(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer()

    server.listen(port, () => {
      server.once('close', () => {
        resolve(true)
      })
      server.close()
    })

    server.on('error', () => {
      resolve(false)
    })
  })
}

async function findAvailablePort(startPort = 30020, count = 10): Promise<number | null> {
  const ports = Array.from({ length: count }, (_, i) => startPort + i)

  for (const port of ports) {
    const isAvailable = await checkPort(port)
    if (isAvailable) {
      return port
    }
  }

  return null
}

export class FileUploadServer {
  private server: http.Server

  constructor() {
    this.server = http.createServer(async (req, res) => {
      await this.handleRequest(req, res)
    })
  }

  public getServer() {
    return this.server
  }

  private async handleRequest(req: http.IncomingMessage, res: http.ServerResponse) {
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

    if (req.method === 'OPTIONS') {
      res.writeHead(200)
      res.end()
      return
    }

    if (req.method === 'POST' && req.url?.startsWith('/upload')) {
      this.fileUpload(req, res)
    } else if (req.method === 'GET' && req.url === '/health') {
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ status: 'ok' }))
    } else if (req.method === 'GET' && req.url?.startsWith('/platform-url')) {
      this.platformUrl(req, res)
    } else if (req.method === 'GET' && req.url?.startsWith('/file')) {
      this.getFile(req, res)
    } else if (req.method === 'GET' && req.url === '/client-id') {
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ clientId: getStore('deviceId') }))
    } else if (req.method === 'POST' && req.url === '/rpa-video') {
      await this.rapVideo(req, res)
    } else if (req.method === 'POST' && req.url === '/rpa-image') {
      await this.rapImage(req, res)
    } else {
      res.writeHead(404)
      res.end('Not Found')
    }
  }

  private async getRequestBody(req: http.IncomingMessage) {
    return new Promise((resolve, reject) => {
      let body = ''

      req.on('data', (chunk) => {
        body += chunk.toString()
      })

      req.on('end', () => {
        try {
          const jsonData = JSON.parse(body)
          resolve(jsonData)
        } catch {
          reject(new Error('Invalid JSON format'))
        }
      })

      req.on('error', (error) => {
        reject(error)
      })
    })
  }

  private async rapImage(req: http.IncomingMessage, res: http.ServerResponse) {
    try {
      if (req.method !== 'POST') {
        res.statusCode = 405
        res.end('Method Not Allowed')
        return
      }

      const contentType = req.headers['content-type']
      if (!contentType || !contentType.includes('application/json')) {
        res.statusCode = 400
        res.end('Content-Type must be application/json')
        return
      }

      // 获取JSON数据
      const jsonData = (await this.getRequestBody(req)) as {
        config: ImageTask & {
          images: string[]
          cookies: Electron.Cookie[]
          localStorage: AnyObject
        }
      }

      // 处理业务逻辑
      void rpaImageStart(jsonData.config)

      // 返回响应
      res.statusCode = 200
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          success: true
        })
      )
    } catch (error) {
      console.error('处理错误:', error)
      res.statusCode = 500
      res.end('Internal Server Error')
    }
  }

  private async rapVideo(req: http.IncomingMessage, res: http.ServerResponse) {
    try {
      if (req.method !== 'POST') {
        res.statusCode = 405
        res.end('Method Not Allowed')
        return
      }

      const contentType = req.headers['content-type']
      if (!contentType || !contentType.includes('application/json')) {
        res.statusCode = 400
        res.end('Content-Type must be application/json')
        return
      }

      // 获取JSON数据
      const jsonData = (await this.getRequestBody(req)) as {
        data: {
          video: string
          cover: string
        }
        config: VideoTask & { cookies: Electron.Cookie[]; localStorage: AnyObject }
      }

      // 处理业务逻辑
      void rpaVideoStart(jsonData.data, jsonData.config)

      // 返回响应
      res.statusCode = 200
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          success: true
        })
      )
    } catch (error) {
      console.error('处理错误:', error)
      res.statusCode = 400
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          success: false,
          error: (error as Error).message
        })
      )
    }
  }

  private getFile(req: http.IncomingMessage, res: http.ServerResponse) {
    const url = new URL(req.url || '', `http://localhost`)
    const filePath = url.searchParams.get('path')
    if (!filePath) {
      res.writeHead(400, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ success: false, error: 'Missing path' }))
      return
    }

    // 安全考虑
    const basename = path.basename(filePath)
    const newFilePath = path.join(appCachePath, 'uploads', basename)

    if (!fs.existsSync(newFilePath)) {
      res.writeHead(404, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ error: 'File not found' }))
      return
    }

    const data = fs.readFileSync(newFilePath)
    res.writeHead(200, { 'Content-Type': 'application/octet-stream' })
    res.end(data)
  }

  private platformUrl(req: http.IncomingMessage, res: http.ServerResponse) {
    const urlParts = req.url!.split('?')
    const queryString = urlParts[1] || ''
    const queryParams = new URLSearchParams(queryString)

    const platformName = queryParams.get('platform')

    if (!platformName) {
      res.writeHead(400, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ error: 'platform parameter is required' }))
      return
    }

    const platform = task.getPlatformService().injection.platformConfig[platformName]
    if (!platform) {
      res.writeHead(404, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ error: 'Platform not found' }))
      return
    }

    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ status: 'ok', platformEntryUrl: platform.entryUrl }))
  }

  private fileUpload(req: http.IncomingMessage, res: http.ServerResponse) {
    const url = new URL(req.url || '', `http://localhost`)
    const filename = url.searchParams.get('filename')
    if (!filename) {
      res.writeHead(400, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ success: false, error: 'Missing filename' }))
      return
    }

    const uploadDir = path.join(appCachePath, 'uploads')

    fs.ensureDirSync(uploadDir)

    const filepath = path.join(uploadDir, filename)

    const writeStream = fs.createWriteStream(filepath)

    req.pipe(writeStream)

    writeStream.on('finish', () => {
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(
        JSON.stringify({
          success: true,
          filepath
        })
      )
    })

    writeStream.on('error', (err) => {
      res.writeHead(500, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ success: false, error: err.message }))
    })
  }

  async start(): Promise<number | null> {
    const port = await findAvailablePort()

    if (port) {
      this.server.listen(port, () => {
        logger.info(`服务器已启动，端口号：${port}`)
        AppContext.getInstance().setPort(port)
      })
      return port
    } else {
      return null
    }
  }

  stop() {
    if (this.server) {
      this.server.close()
    }
  }
}
