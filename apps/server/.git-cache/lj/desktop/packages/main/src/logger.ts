import { appCachePath } from 'electron-global/appCachePath'
import log4js from 'log4js'
import path from 'path'

const loggerCache = new Map<string, log4js.Logger>()

export type LoggerType = 'error' | 'warn' | 'info' | 'debug'

log4js.configure({
  appenders: {
    dev: {
      type: 'console'
    },
    prod: {
      type: 'dateFile',
      filename: path.join(appCachePath, 'log'),
      pattern: 'yyyy-MM-dd',
      alwaysIncludePattern: true,
      keepFileExt: true,
      numBackups: 24 * 15
    }
  },
  categories: {
    default: {
      appenders: [import.meta.env.DEV ? 'dev' : 'prod'],
      level: 'trace'
    }
  }
})

function getLogger(type: string) {
  if (!loggerCache.has(type)) {
    const logger = log4js.getLogger(type)
    loggerCache.set(type, logger)
  }
  return loggerCache.get(type)!
}

const logEmojiMap: Record<LoggerType, string> = {
  error: '❗️',
  warn: '⚠️',
  info: 'ℹ️',
  debug: '🔎'
}

export const Logger = {
  getLogger,
  logEmojiMap
}
