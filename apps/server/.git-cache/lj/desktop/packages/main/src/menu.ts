import { app, BrowserWindow, Menu, Tray, screen, dialog, nativeImage } from 'electron'
import { getAssetPath } from './constant'
import { isWin } from 'electron-global/platform'
import { AppContext } from './context'
import { Logger } from './logger'

const logger = Logger.getLogger('menu')

let tray: Tray

export function showWindow(win?: BrowserWindow | null) {
  if (!win) {
    return
  }

  win.isMinimized() && win.restore()

  if (!win.isVisible()) {
    const mousePoint = screen.getCursorScreenPoint()

    const display = screen.getDisplayNearestPoint(mousePoint)

    const windowBounds = win.getBounds()

    const centerX = display.bounds.x + Math.floor((display.bounds.width - windowBounds.width) / 2)
    const centerY = display.bounds.y + Math.floor((display.bounds.height - windowBounds.height) / 2)

    win.setBounds({
      x: centerX,
      y: centerY,
      width: windowBounds.width,
      height: windowBounds.height
    })

    AppContext.getInstance().getWebviewManager().updateTabBoundAll()
  }

  win.show()
  win.setAlwaysOnTop(true)
  win.setSkipTaskbar(false)
  win.setAlwaysOnTop(false)
}

function getCurrentWindows() {
  return BrowserWindow.getAllWindows()
}

function windowsDoubleClickTray() {
  const currentWindows = getCurrentWindows()[0]

  if (currentWindows) {
    void showWindow(currentWindows)
  }
}

export function updateTrayMenu(isClose = true) {
  if (isWin) {
    const image = nativeImage.createFromPath(
      getAssetPath('tray', isClose ? 'windows-tray-close.png' : 'windows-tray.png')
    )

    tray = tray ?? new Tray(image)

    tray.setImage(image)
  } else {
    tray = tray ?? new Tray(getAssetPath('tray', 'macOSTemplate.png'))
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: `显示`,
      click(_, __) {
        AppContext.getInstance().getMainWindow()?.show()
      }
    },
    {
      label: `关于${import.meta.env.VITE_APP_NAME}`,
      click(_, __) {
        void dialog.showMessageBox({
          type: 'info',
          title: import.meta.env.VITE_APP_NAME,
          buttons: ['好的'],
          message: `当前版本: ${__APP_VERSION__}`
        })
      }
    },
    { type: 'separator' },
    {
      label: `退出`,
      click: () => {
        app.exit()
      }
    }
  ])

  tray.setToolTip(import.meta.env.VITE_APP_NAME)
  tray.setContextMenu(contextMenu)
  if (isWin) {
    tray.removeListener('double-click', windowsDoubleClickTray)
    tray.on('double-click', windowsDoubleClickTray)
  }
}

export function setAppMenu() {
  const menu = Menu.buildFromTemplate([
    {
      label: '查看',
      submenu: [
        {
          label: `关于${import.meta.env.VITE_APP_NAME}`,
          click(_, focusedWindow) {
            if (focusedWindow) {
              void dialog.showMessageBox(focusedWindow, {
                type: 'info',
                title: import.meta.env.VITE_APP_NAME,
                buttons: ['好的'],
                message: `当前版本: ${__APP_VERSION__}`
              })
            }
          }
        },
        {
          label: '退出',
          accelerator: 'Cmd+Q',
          role: 'quit'
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        {
          label: '撤销',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo'
        },
        {
          type: 'separator'
        },
        {
          label: '剪切',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut'
        },
        {
          label: '复制',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: '粘贴',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        },
        {
          label: '全选',
          accelerator: 'CmdOrCtrl+A',
          role: 'selectAll'
        },
        {
          type: 'separator'
        },
        {
          label: '关闭当前网页',
          accelerator: 'CommandOrControl+W',
          click() {
            if (AppContext.getInstance().getMainWindow()?.isFocused()) {
              AppContext.getInstance().getWebviewManager().closeActiveTab()
            }
          }
        },
        {
          label: '打开新的标签页',
          accelerator: 'CommandOrControl+T',
          click() {
            if (AppContext.getInstance().getMainWindow()?.isFocused()) {
              void AppContext.getInstance().getWebviewManager().createNewTabPage()
            }
          }
        },
        {
          label: '刷新当前网页',
          accelerator: 'CommandOrControl+R',
          click() {
            if (AppContext.getInstance().getMainWindow()?.isFocused()) {
              const activeTab = AppContext.getInstance().getWebviewManager().getActiveTab()
              if (!activeTab) {
                return
              }
              const view = AppContext.getInstance().getWebviewManager().getView(activeTab.id)
              if (view) {
                try {
                  const urlObj = new URL(activeTab.url)
                  const params = new URLSearchParams(urlObj.search)

                  const originalUrl = params.get('originalUrl')

                  if (originalUrl) {
                    void view.webContents.loadURL(decodeURIComponent(originalUrl))
                    return
                  }
                } catch (error) {
                  logger.error('setAppMenu', error)
                }

                view.webContents.reload()
              }
            }
          }
        }
      ]
    }
  ])
  Menu.setApplicationMenu(menu)
}
