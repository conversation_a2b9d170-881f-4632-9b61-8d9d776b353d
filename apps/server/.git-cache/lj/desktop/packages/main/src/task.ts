/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/consistent-type-imports */
import { app } from 'electron'
import { reptileFilePath } from './constant'
import path from 'path'
import fs from 'fs-extra'
import { Platforms } from '@yixiaoer/platform-service'
import { PlatformAccountInfo } from '@yixiaoer/platform-service/dist/cloud/model/BaseTaskFormModel'

export interface CachedAccountInfo {
  id: string
  platformCode: Platforms
  platformUserId: string
  platformUserName: string
  platformCookie: string
}

export const toPlatformAccountInfo = (account: CachedAccountInfo): PlatformAccountInfo => {
  return {
    platformAccountId: account.id,
    platformAuthorId: account.platformUserId,
    platformAccountName: account.platformUserName,
    cookie: account.platformCookie,
  }satisfies PlatformAccountInfo
}

export const task = new (class Task {
  getPlatformService() {
    if (!fs.existsSync(reptileFilePath)) {
      const reptile =
        require('@yixiaoer/platform-service') as typeof import('@yixiaoer/platform-service')
      const config = reptile.PubConfig

      config.API_URL = import.meta.env.VITE_APP_API.replace('/api', '')
      config.setUserDataPath(path.join(app.getPath('userData'), 'platform-service'))
      return reptile
    }

    delete require.cache[reptileFilePath]
    const reptile = eval(
      `require(${JSON.stringify(reptileFilePath)})`
    ) as typeof import('@yixiaoer/platform-service')
    const config = reptile.PubConfig

    config.API_URL = import.meta.env.VITE_APP_API.replace('/api', '')
    config.setUserDataPath(path.join(app.getPath('userData'), 'platform-service'))

    return reptile
  }
})()
