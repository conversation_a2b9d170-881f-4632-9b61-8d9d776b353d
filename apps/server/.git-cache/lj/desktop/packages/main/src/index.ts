import { app, ipc<PERSON>ain, <PERSON>u, shell } from 'electron'

import { createMainWindow } from './mainWindow'
import { registerFileProtocol } from './registerFileProtocol'
import { setAppMenu, showWindow } from './menu'
import { isWin } from 'electron-global/platform'

import { Logger } from './logger'
import { createServerSocketServer } from './serverSocket'
import { createServer } from './createServer'
import { registerShortcut } from './registerShortcut'
import { AppContext } from './context'
import { appCachePath } from 'electron-global/appCachePath'

const logger = Logger.getLogger('main')
app.commandLine.appendSwitch('lang', 'zh-CN')

if (isWin) {
  app.setAppUserModelId(import.meta.env.VITE_APP_APPID)
} else {
  //
}

/**
 * 防止 electron 运行多个实例
 */
const isSingleInstance = app.requestSingleInstanceLock()

if (!isSingleInstance) {
  app.quit()
  process.exit(0)
} else {
  app.on('second-instance', () => {
    // 显示并聚焦已存在的窗口
    showWindow(AppContext.getInstance().getMainWindow())
  })
}

/**
 * 如果所有窗口都已关闭，则退出后台进程
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

/**
 * @see https://www.electronjs.org/docs/latest/api/app#event-activate-macos Event: 'activate'.
 */
app.on('activate', () => {
  const window = AppContext.getInstance().getMainWindow()
  if (!window) {
    void createMainWindow()
  } else {
    showWindow(window)
  }
})

if (import.meta.env.DEV) {
  app.on('certificate-error', (event, _webContents, _url, _error, _certificate, callback) => {
    event.preventDefault()
    callback(true)
  })
}

if (!__IS_PROD__) {
  void app.whenReady().then(() => {
    const menu = Menu.buildFromTemplate([
      {
        label: '打开开发者工具',
        click() {
          AppContext.getInstance().getMainWindow()?.webContents.toggleDevTools()
        }
      },
      {
        label: '打开缓存目录',
        click() {
          void shell.openPath(appCachePath)
        }
      }
    ])

    ipcMain.handle('dev-tools', () => {
      menu.popup()
    })
  })
}

app
  .whenReady()
  .then(setAppMenu)
  .then(registerFileProtocol)
  .then(createServer)
  .then(createMainWindow)
  .then(registerShortcut)
  .then(createServerSocketServer)
  .catch((e) => logger.error('创建失败的窗口:', e))
