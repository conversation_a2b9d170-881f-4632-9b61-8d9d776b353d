import * as os from 'os'
import Store from 'electron-store'

const store = new Store()

export function setStore(key: string, value: Placeholder) {
  store.set(key, value)
}

export function getStore<T>(key: string) {
  return store.get(key) as T
}

export function isWindows11(): boolean {
  if (process.platform !== 'win32') {
    return false // 不是 Windows 系统
  }

  // 获取 Windows 版本信息
  const release = os.release().split('.')

  // Windows 11 的主要版本号是 10.0，但内部版本号 >= 22000
  if (release[0] === '10' && release[1] === '0') {
    // 获取内部版本号 (build number)
    const buildNumber = parseInt(release[2], 10)
    return buildNumber >= 22000
  }

  return false
}
