import { FileProtocol } from 'electron-global/fileProtocol'
import { appCachePath } from 'electron-global/appCachePath'
import type { BaseWindow } from 'electron'
import {
  systemPreferences,
  ipcMain,
  type BrowserWindow,
  globalShortcut,
  dialog,
  shell,
  Menu
} from 'electron'
import { type SendMessageType } from './types'
import { InstructEnum, IpcChannelEnum, WindowsInstructEnum } from 'electron-global/enum'
import { updateTrayMenu } from './menu'
import { homedir } from 'os'
import path from 'path'
import fs from 'fs-extra'
import { Logger, type LoggerType } from './logger'
import { openFileDir } from './openFileDir'
import { createMainWindow } from './mainWindow'
import { AppContext } from './context'
import { toolPagePath } from './pagePath'
import { createOnlineWindow } from './createOnlineWindow'

const logger = Logger.getLogger('instruct')

export interface InstructEvents {
  browserWindow: BrowserWindow
}

class Instruct {
  /**
   * render 进程实例
   */
  private apps: Record<PropertyKey, BrowserWindow | BaseWindow> = {}

  /**
   * 子窗口
   */
  public childWindowMap: Record<string, BrowserWindow | BaseWindow> = {}

  /**
   * 窗口对应的badge
   */
  public userBadgeMap: Record<string, number> = {}

  constructor() {
    ipcMain.handle(IpcChannelEnum.Public, (events, data: SendMessageType) => {
      const event = this.apps[events.sender.id]
      return this.dispatchers(event, data)
    })
  }

  /**
   * 缓存render进程实例
   * @param id
   * @param events
   */
  public initializationValue(id: number, browserWindow: BrowserWindow | BaseWindow) {
    this.apps[id] = browserWindow
  }

  public deleteValue(id: number) {
    delete this.apps[id]
  }

  /**
   * 分发信道处理
   * @param event
   * @param sendMessage
   * @returns
   */
  private async dispatchers(
    browserWindow: BrowserWindow | BaseWindow,
    sendMessage: SendMessageType
  ) {
    const { type, data } = sendMessage

    switch (type) {
      case InstructEnum.SystemPreferences:
        if (systemPreferences.askForMediaAccess) {
          return systemPreferences.askForMediaAccess('microphone')
        }
        return true
      case InstructEnum.RegistrationShortcutDisplayWindow:
        try {
          if (data.oldShort) {
            globalShortcut.unregister((data.oldShort as string).replace(/\s/g, ''))
          }

          if (data.newShort) {
            globalShortcut.register((data.newShort as string).replace(/\s/g, ''), () => {
              browserWindow.focus()

              if (browserWindow.isMinimized()) {
                browserWindow.restore()
              }
            })
          }
        } catch (error) {
          logger.error(error)
        }

        break
      case InstructEnum.CheckUpdate:
        void AppContext.getInstance().getUpdaterManager().checkUpdate()
        break
      case InstructEnum.SaveAsFile: {
        const { canceled, filePath } = await dialog.showSaveDialog(browserWindow, {
          defaultPath: path.join(homedir(), data.fileName)
        })

        if (!(canceled || !filePath)) {
          fs.copyFileSync((data.localUrl as string).replace(FileProtocol, ''), filePath)
        }
        break
      }
      case InstructEnum.ShowMoreMenu:
        {
          const menu = Menu.buildFromTemplate([
            // {
            //   label: '检测更新',
            //   click: () => {
            //     void AppContext.getInstance().getUpdaterManager().checkUpdate()
            //   }
            // },
            {
              label: '在线状态',
              click: () => {
                void createOnlineWindow()
              }
            }
          ])
          menu.popup({ window: browserWindow })
        }

        break
      case InstructEnum.Hide:
        browserWindow.hide()
        break
      /**
       * 最小化 window
       * @description 针对 windows
       */
      case WindowsInstructEnum.WindowMin:
        browserWindow.minimize()
        break
      /**
       * 最大化window
       * @description 针对 windows
       */
      case WindowsInstructEnum.WindowMax:
        if (browserWindow.isMaximized()) {
          browserWindow.restore()
        } else {
          browserWindow.maximize()
        }
        break
      /**
       * 关闭窗口
       * @description 针对 windows
       */
      case WindowsInstructEnum.WindowClose:
        browserWindow.close()
        break
      case InstructEnum.GetToolPath:
        return toolPagePath
      case InstructEnum.isFocus:
        return browserWindow.isFocused()
      case InstructEnum.MacosSendNotification:
        browserWindow.focus()
        // browserWindow.webContents.send(IpcChannelEnum.NoticeRender, data)
        break
      case InstructEnum.NativeDialog:
        return dialog.showMessageBox(browserWindow, data)
      case InstructEnum.Quit:
        browserWindow.destroy()
        updateTrayMenu()
        break
      case InstructEnum.SetTheme:
        fs.outputJSONSync(path.join(appCachePath, 'theme.json'), data)
        browserWindow.setBackgroundColor(data.color)
        break
      case InstructEnum.Logger:
        {
          const logger = Logger.getLogger(data.type)
          const emoji = Logger.logEmojiMap[data.level as LoggerType] || ''
          logger[data.level as LoggerType](`${emoji}`, ...data.texts)
        }
        break
      case InstructEnum.OpenExternal:
        void shell.openExternal(data)
        break
      case InstructEnum.OpenDir:
        void openFileDir(data)
        break
      case InstructEnum.Login:
        await createMainWindow()
        browserWindow.close()
        break
      default:
        break
    }
  }
}

export const instruct = new Instruct()
