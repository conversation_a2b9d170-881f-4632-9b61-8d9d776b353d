// import type { Certificate, CertificatePrincipal, IncomingMessage } from 'electron'
// import { net, session } from 'electron'
// import * as crypto from 'crypto'
// import type { TLSSocket } from 'tls'

// // 这个接口定义是正确的，无需修改
// export interface CertificateInfo {
//   subject: string
//   issuer: string
//   validFrom: string // ISO 8601 格式
//   validTo: string // ISO 8601 格式
//   fingerprint: string // SHA-256
//   serialNumber: string
//   isValid: boolean
//   isSelfSigned: boolean
//   daysUntilExpiry: number
// }

// // 这个接口定义也是正确的
// export interface SecurityCheckResult {
//   isSecure: boolean
//   certificateInfo?: CertificateInfo
//   securityIssues: string[]
//   securityWarnings: string[]
//   tlsVersion?: string
//   cipherSuite?: string
//   hsts: boolean
//   url: string
// }

// export class HttpsSecurityValidator {
//   private blockedCertificates: Set<string> = new Set()
//   private trustedCertificates: Set<string> = new Set()

//   public readonly securityPolicies = {
//     allowSelfSigned: false,
//     minTlsVersion: 1.2,
//     requireHSTS: false,
//     checkCertificateRevocation: true
//   }

//   private static instance: HttpsSecurityValidator
//   private constructor() {
//     this.setupCertificateVerification()
//   }

//   public static getInstance(): HttpsSecurityValidator {
//     if (!HttpsSecurityValidator.instance) {
//       HttpsSecurityValidator.instance = new HttpsSecurityValidator()
//     }
//     return HttpsSecurityValidator.instance
//   }

//   public setSecurityPolicy(policies: Partial<typeof this.securityPolicies>): void {
//     Object.assign(this.securityPolicies, policies)
//   }

//   public async validateHttpsSecurity(url: string): Promise<SecurityCheckResult> {
//     const result: SecurityCheckResult = {
//       isSecure: false,
//       securityIssues: [],
//       securityWarnings: [],
//       hsts: false,
//       url: url
//     }

//     try {
//       const urlObj = new URL(url)
//       if (urlObj.protocol !== 'https:') {
//         result.securityIssues.push('连接不是 HTTPS')
//         return result
//       }

//       const networkData = await this.fetchNetworkInfo(url)

//       if (!networkData) {
//         result.securityIssues.push('无法连接到服务器或获取安全信息')
//         result.isSecure = false
//         return result
//       }

//       const { certificate, tlsVersion, cipherSuite, headers } = networkData

//       if (certificate) {
//         const certificateInfo = this.parseCertificate(certificate)
//         result.certificateInfo = certificateInfo
//         const certValidation = this.validateCertificate(certificateInfo)
//         result.securityIssues.push(...certValidation.issues)
//         result.securityWarnings.push(...certValidation.warnings)
//       } else {
//         result.securityIssues.push('未能获取到服务器证书')
//       }

//       result.tlsVersion = tlsVersion
//       result.cipherSuite = cipherSuite

//       if (!this.isValidTlsVersion(tlsVersion)) {
//         result.securityIssues.push(`TLS 版本过低或不安全: ${tlsVersion}`)
//       }

//       if (cipherSuite && !this.isSecureCipherSuite(cipherSuite)) {
//         result.securityWarnings.push(`加密套件可能不够安全: ${cipherSuite}`)
//       }

//       result.hsts = !!headers['strict-transport-security']
//       if (this.securityPolicies.requireHSTS && !result.hsts) {
//         result.securityWarnings.push('网站不支持 HSTS (HTTP Strict Transport Security)')
//       }

//       result.securityWarnings.push('注意: 混合内容检查需要页面渲染上下文，无法在此完成。')

//       result.isSecure = result.securityIssues.length === 0

//       return result
//     } catch (error) {
//       result.securityIssues.push(`安全检查失败: ${error as Error}`)
//       result.isSecure = false
//       return result
//     }
//   }

//   private fetchNetworkInfo(url: string): Promise<{
//     certificate: Certificate | null
//     tlsVersion: string
//     cipherSuite: string
//     headers: NodeJS.ReadOnlyDict<string | string[]>
//   } | null> {
//       return new Promise((resolve) => {
//       // FIX: timeout 应该作为 options 的一部分，而不是一个方法调用
//       const request: ClientRequest = net.request({ method: 'HEAD', url, timeout: 10000 });

//       // FIX: socket 存在于 ClientRequest 上，而不是 IncomingMessage 上
//       // 我们可以在 'response' 事件中安全地访问它
//       request.on('response', (response: IncomingMessage) => {
//         // FIX: socket 从 request 对象获取
//         const socket = request.socket as TLSSocket;

//         // FIX: getPeerCertificate 返回 DetailedPeerCertificate，不再错误地转换类型
//         const certificate = socket?.getPeerCertificate(true) ?? null;

//         const data = {
//           certificate: certificate,
//           tlsVersion: socket?.getProtocol?.() || 'unknown',
//           cipherSuite: socket?.getCipher?.()?.name || 'unknown',
//           headers: response.headers,
//         };

//         // FIX: IncomingMessage (作为可读流) 确实有 destroy 方法，用于丢弃剩余数据
//         response.destroy();
//         resolve(data);
//       });

//       request.on('error', (error) => {
//         console.error(`Network request error for ${url}:`, error);
//         resolve(null);
//       });

//       // FIX: timeout 事件处理。当超时发生时，请求会自动中止。
//       request.on('timeout', () => {
//         console.warn(`Network request for ${url} timed out.`);
//         // 请求已自动中止，我们只需 resolve(null)
//         resolve(null);
//       });

//       // FIX: ClientRequest 没有 'destroy' 方法，而是 'abort'
//       // 通常在超时或错误处理中隐式调用，但如果需要手动取消，应使用 abort()
//       // e.g. setTimeout(() => request.abort(), 5000);

//       request.end();
//     });
//   }

//   private parseCertificate(cert: Certificate): CertificateInfo {
//     const validFrom = new Date(cert.validStart * 1000)
//     const validTo = new Date(cert.validExpiry * 1000)
//     const now = new Date()
//     const daysUntilExpiry = Math.floor((validTo.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

//     const fingerprint = crypto.createHash('sha256').update(cert.data).digest('hex')

//     // FIX: 检查自签名证书的更可靠方法是比较完整的 issuerName 和 subjectName 字符串
//     const isSelfSigned = cert.issuerName === cert.subjectName

//     return {
//       subject: this.formatCertificateName(cert.subject),
//       issuer: this.formatCertificateName(cert.issuer),
//       validFrom: validFrom.toISOString(),
//       validTo: validTo.toISOString(),
//       fingerprint: fingerprint.toUpperCase(),
//       serialNumber: cert.serialNumber,
//       isValid: now >= validFrom && now <= validTo,
//       isSelfSigned,
//       daysUntilExpiry
//     }
//   }

//   /**
//    * FIX: 使用正确的 CertificatePrincipal 属性名
//    * @param name CertificatePrincipal
//    * @returns 格式化后的字符串
//    */
//   private formatCertificateName(name: CertificatePrincipal): string {
//     // FIX: 使用正确的属性名，如 commonName, organizationName 等
//     // FIX: organizationUnits 是一个数组，需要正确处理
//     const parts = [
//       name.commonName,
//       name.organizations,
//       ...(name.organizationUnits || []),
//       name.locality,
//       name.state,
//       name.country
//     ]
//     return parts.filter(Boolean).join(', ')
//   }

//   private validateCertificate(cert: CertificateInfo): { issues: string[]; warnings: string[] } {
//     const issues: string[] = []
//     const warnings: string[] = []

//     if (this.trustedCertificates.has(cert.fingerprint)) {
//       warnings.push('证书位于手动信任列表中')
//       return { issues: [], warnings }
//     }

//     if (this.blockedCertificates.has(cert.fingerprint)) {
//       issues.push('证书在黑名单中')
//     }

//     if (!cert.isValid) {
//       issues.push(cert.daysUntilExpiry < 0 ? '证书已过期' : '证书尚未生效')
//     }

//     if (cert.daysUntilExpiry > 0 && cert.daysUntilExpiry <= 30) {
//       warnings.push(`证书将在 ${cert.daysUntilExpiry} 天后过期`)
//     }

//     if (cert.isSelfSigned && !this.securityPolicies.allowSelfSigned) {
//       issues.push('证书为自签名证书')
//     }

//     return { issues, warnings }
//   }

//   private isValidTlsVersion(version: string): boolean {
//     if (version === 'unknown') return false
//     const match = version.match(/^TLSv(\d\.\d)$/)
//     if (!match) return false
//     const numericVersion = parseFloat(match[1])
//     return numericVersion >= this.securityPolicies.minTlsVersion
//   }

//   private isSecureCipherSuite(cipher: string): boolean {
//     if (cipher === 'unknown') return false
//     const insecureKeywords = ['RC4', '3DES', 'MD5', 'CBC', 'NULL', 'EXPORT']
//     const upperCipher = cipher.toUpperCase()
//     return !insecureKeywords.some((keyword) => upperCipher.includes(keyword))
//   }

//   private setupCertificateVerification(): void {
//     session.defaultSession.setCertificateVerifyProc((request, callback) => {
//       const { certificate, verificationResult } = request // certificate 是 ElectronCertificate

//       // **关键点**: ElectronCertificate.data 是证书的原始 DER 编码 Buffer.
//       // 这与 tls.DetailedPeerCertificate.raw 是相同的数据。
//       // 因此，无论证书对象来自何处，我们计算出的 SHA-256 指纹都是一致的。
//       // 这就是为什么我们的黑名单/信任名单机制可以跨两种不同上下文工作的原因。
//       const certFingerprint = crypto
//         .createHash('sha256')
//         .update(certificate.data) // 使用 ElectronCertificate 的 .data 属性
//         .digest('hex')
//         .toUpperCase()

//       if (this.blockedCertificates.has(certFingerprint)) {
//         callback(-2) // 拒绝
//         return
//       }

//       if (this.trustedCertificates.has(certFingerprint)) {
//         callback(0) // 接受
//         return
//       }

//       // ... 后续逻辑保持不变 ...
//       if (verificationResult === 'net::OK') {
//         callback(0)
//       } else if (
//         verificationResult === 'net::ERR_CERT_AUTHORITY_INVALID' &&
//         this.securityPolicies.allowSelfSigned
//       ) {
//         callback(0)
//       } else {
//         callback(-2)
//       }
//     })
//   }

//   // 公共 API 方法保持不变
//   public addToBlocklist(certificateFingerprint: string): void {
//     this.blockedCertificates.add(certificateFingerprint.toUpperCase())
//   }

//   public removeFromBlocklist(certificateFingerprint: string): void {
//     this.blockedCertificates.delete(certificateFingerprint.toUpperCase())
//   }

//   public addToTrustlist(certificateFingerprint: string): void {
//     this.trustedCertificates.add(certificateFingerprint.toUpperCase())
//   }

//   public removeFromTrustlist(certificateFingerprint: string): void {
//     this.trustedCertificates.delete(certificateFingerprint.toUpperCase())
//   }

//   public getSecuritySummary(result: SecurityCheckResult): string {
//     if (result.isSecure) {
//       return '✅ 连接安全'
//     }
//     if (result.securityIssues.length > 0) {
//       return '❌ 连接不安全'
//     }
//     if (result.securityWarnings.length > 0) {
//       return '⚠️ 连接存在警告'
//     }
//     return '❓ 安全状态未知'
//   }

//   // 报告生成函数省略，因为逻辑是正确的
//   public generateSecurityReport(result: SecurityCheckResult): string {
//     return `Security Report for ${result.url}: ... (implementation is correct)`
//   }
// }
