import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'electron'
import { <PERSON><PERSON>er<PERSON>indow, WebContentsView, ipcMain, session } from 'electron'
import type { TabInfo } from 'electron-global/webview'
import { EventEmitter } from 'events'
import { errorPagePath, placeholderPagePath } from './pagePath'
import { saveAuth } from './business/auth'
import { createContextMenu } from './contextMenu'
import { AppContext } from './context'
import { getStore } from './utils'
import { getAssetPath } from './constant'
import { Logger } from './logger'
import { task } from './task'
import { noop } from 'electron-global/noop'

const logger = Logger.getLogger('webview')

const defaultSessionKey = 'persist:default-session'

function wait(time = 1000) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

export interface NavigationInfo {
  url: string
  title: string
  canGoBack: boolean
  canGoForward: boolean
  isLoading: boolean
  progress: number
}

interface StorageData {
  [key: string]: string
}

export class WebviewManager extends EventEmitter {
  private window: BaseWindow
  private tabs: Map<string, WebContentsView> = new Map()
  private tabStates: Map<string, TabInfo> = new Map()
  private activeTabId: string | null = null
  private homeTabId: string | null = null
  private tabCounter = 0
  private tabInjectReady: Map<string, boolean> = new Map()
  private preloadPath?: string
  private subscribers: Set<Electron.WebContents> = new Set()

  private registeredProtocolSessions: Set<string> = new Set()

  constructor(window: BaseWindow, preloadPath?: string) {
    super()
    this.window = window
    this.preloadPath = preloadPath
    this.setupIPC()
    this.setupWindowEvents()

    void this.createHomeTabPage()
  }

  public getView(tabId: string) {
    return this.tabs.get(tabId)
  }

  public getNewTabPages() {
    const views: WebContentsView[] = []
    this.tabStates.forEach((state) => {
      if (state.isNewTabPage) {
        const view = this.getView(state.id)
        if (view) {
          views.push(view)
        }
      }
    })

    return views
  }

  async createHomeTabPage() {
    this.homeTabId = await this.createTab(import.meta.env.VITE_APP_HOME, {
      activate: true,
      isNewTabPage: true,
      isHome: true
    })
  }

  createNewTabPage(): Promise<string> {
    return this.createTab('about:blank', { activate: true, isNewTabPage: true })
  }

  createAuthTabPage(url: string, extra: AnyObject) {
    return this.createTab(url, { activate: true, isAuth: true, extra })
  }

  createRestoreAuthTabPageByRpa(
    url: string,
    cookies: Electron.Cookie[],
    localStorage: AnyObject,
    accountId: string,
    authorizeUrl: string,
    title?: string
  ) {
    return this.createTab(url, {
      activate: true,
      cookies,
      preload: true,
      localStorage,
      extra: { accountId, spaceName: title, authorizeUrl }
    })
  }

  createRestoreAuthTabPage(
    url: string,
    cookies: Electron.Cookie[],
    localStorage: AnyObject,
    extra: {
      openId: string
      type: string
      accountId: string
      spaceName?: string
      platformName?: string
      isUpdateAuth?: boolean
    }
  ) {
    return this.createTab(url, {
      activate: true,
      cookies,
      isUpdateAuth: extra.isUpdateAuth ?? true,
      preload: true,
      localStorage,
      extra
    })
  }

  private registerProtocolHandler(ses: Electron.Session, partition: string) {
    if (this.registeredProtocolSessions.has(partition)) {
      return
    }

    try {
      ses.protocol.handle('bitbrowser', () => {
        return new Response('bitbrowser protocol is not implemented', {
          status: 404,
          headers: {
            'Content-Type': 'text/plain'
          }
        })
      })
      ses.protocol.handle('bytedance', () => {
        return new Response('bytedance protocol is not implemented', {
          status: 404,
          headers: {
            'Content-Type': 'text/plain'
          }
        })
      })

      this.registeredProtocolSessions.add(partition)
    } catch (error) {
      logger.error(`注册协议处理器失败:`, error)
    }
  }

  // 新增：清理协议处理器
  private unregisterProtocolHandler(ses: Electron.Session, partition: string) {
    if (!this.registeredProtocolSessions.has(partition)) {
      return
    }

    try {
      // 检查是否还有其他tab在使用同一个session
      const hasOtherTabsUsingSession = Array.from(this.tabStates.values()).some((tab) => {
        const tabPartition = this.getPartitionForTab(tab)
        return tabPartition === partition
      })

      if (!hasOtherTabsUsingSession) {
        ses.protocol.unhandle('bitbrowser')
        this.registeredProtocolSessions.delete(partition)
      }
    } catch (error) {
      logger.error(`清理协议处理器失败:`, error)
    }
  }

  // 新增：获取tab对应的partition
  private getPartitionForTab(tab: TabInfo): string {
    if (tab.extra && tab.extra.accountId) {
      return `persist:auth-${tab.extra.accountId}`
    } else if (tab.extra && tab.extra.isCreate) {
      return `${defaultSessionKey}-create`
    }
    return defaultSessionKey
  }

  async createTab(
    url = 'about:blank',
    {
      activate,
      isHome,
      isAuth,
      isUpdateAuth,
      isNewTabPage,
      extra,
      localStorage,
      cookies,
      preload
    }: {
      isHome?: boolean
      activate?: boolean
      isAuth?: boolean
      isUpdateAuth?: boolean
      isNewTabPage?: boolean
      extra?: AnyObject
      preload?: boolean
      localStorage?: StorageData
      cookies?: Electron.Cookie[]
    } = {}
  ): Promise<string> {
    const tabId = `tab-${++this.tabCounter}`

    let ses = session.fromPartition(defaultSessionKey)
    let pt = defaultSessionKey

    if (extra && extra.accountId) {
      ses = session.fromPartition(`persist:auth-${extra.accountId}`)
      pt = `persist:auth-${extra.accountId}`
    } else if (extra && extra.isCreate) {
      ses = session.fromPartition(`${defaultSessionKey}-create`)
      pt = `${defaultSessionKey}-create`
    }

    const view = new WebContentsView({
      webPreferences: {
        webSecurity:
          extra?.platformName === task.getPlatformService().injection.platformNames.BaiJiaHao
            ? false
            : true,
        partition: pt,
        session: ses,
        preload: preload ? this.preloadPath : undefined
      }
    })

    // 使用新的协议注册方法
    this.registerProtocolHandler(ses, pt)

    createContextMenu({
      window: view,
      showInspectElement: !__IS_PROD__
    })

    view.webContents.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
    )

    const tabState: TabInfo = {
      domain: this.getDomain(url),
      id: tabId,
      title: '新标签页',
      url: this.normalizeUrl(url),
      isHome,
      isLoading: false,
      canGoBack: false,
      canGoForward: false,
      isActive: false,
      alias: extra?.spaceName,
      isUpdateAuth,
      isAuth,
      isClose: this.calculateIsClose(!!isNewTabPage),
      isNewTabPage: isNewTabPage,
      extra: extra ?? {}
    }

    this.tabs.set(tabId, view)
    this.tabStates.set(tabId, tabState)

    this.setupTabEvents(tabId, view)

    if (localStorage && cookies) {
      await this.restoreCookies(
        tabId,
        {
          cookies,
          localStorage
        },
        extra?.authorizeUrl || url
      )
    }

    if (isNewTabPage) {
      const port = AppContext.getInstance().getPort()
      void view.webContents
        .loadURL(`${placeholderPagePath}?port=${port}&deviceId=${getStore('deviceId') as string}`)
        .catch(noop)
    } else if (url !== 'about:blank') {
      this.navigateTab(tabId, url)
    }

    if (activate) {
      this.switchToTab(tabId)
    }

    this.updateAllTabsCloseStatus()

    this.emit('tab-created', tabState)
    this.broadcastTabsChanged()

    return tabId
  }

  /**
   * 计算标签页是否可以关闭
   */
  private calculateIsClose(isNewTabPage: boolean): boolean {
    const allTabs = Array.from(this.tabStates.values())
    const totalTabs = allTabs.length + 1

    if (isNewTabPage) {
      return totalTabs > 1
    } else {
      return true
    }
  }

  /**
   * 更新所有标签页的可关闭状态
   */
  private updateAllTabsCloseStatus(): void {
    const allTabs = Array.from(this.tabStates.values())
    const totalTabs = allTabs.length

    allTabs.forEach((tab) => {
      if (tab.isNewTabPage) {
        tab.isClose = totalTabs > 1
      } else {
        tab.isClose = true
      }
    })

    this.broadcastTabsChanged()
  }

  /**
   * 切换到指定标签页
   */
  switchToTab(tabId: string): boolean {
    const view = this.tabs.get(tabId)
    const state = this.tabStates.get(tabId)

    if (!view || !state) {
      logger.error(`Tab ${tabId} not found`)
      return false
    }

    if (this.activeTabId && this.activeTabId !== tabId) {
      const currentView = this.tabs.get(this.activeTabId)
      const currentState = this.tabStates.get(this.activeTabId)
      if (currentView && currentState) {
        this.window.contentView.removeChildView(currentView)
        currentState.isActive = false
      }
    }

    // 显示新标签页
    this.window.contentView.addChildView(view)

    state.isActive = true
    this.activeTabId = tabId

    // 更新边界
    this.updateTabBounds(tabId)

    // 更新导航状态
    this.updateNavigationState(tabId)

    this.emit('tab-switched', state)
    this.emit('navigation-changed', this.getNavigationInfo(tabId))
    this.broadcastTabsChanged()
    this.broadcastNavigationChanged(tabId)

    return true
  }

  public closeActiveTab() {
    if (this.activeTabId) {
      this.closeTab(this.activeTabId)
    }
  }

  public closeAll() {
    const allTabs = Array.from(this.tabStates.values())
    allTabs.forEach((tab) => {
      this.closeTab(tab.id)
    })
  }

  closeOtherTabs(tabId: string) {
    const allTabs = Array.from(this.tabStates.values())
    allTabs.forEach((tab) => {
      if (tab.id !== tabId) {
        this.closeTab(tab.id)
      }
    })
  }

  getTabSize(): number {
    return this.tabs.size
  }

  /**
   * 关闭标签页
   */
  closeTab(tabId: string) {
    const view = this.tabs.get(tabId)
    const state = this.tabStates.get(tabId)

    if (!view || !state) {
      logger.error(`closeTab Tab ${tabId} not found`)
      return false
    }

    const allTabs = Array.from(this.tabStates.values())

    if (state.isHome) {
      if (allTabs.length === 1) {
        this.window.hide()
      }

      return
    }

    // 如果是活跃标签页，需要切换到其他标签页
    if (this.activeTabId === tabId) {
      const remainingTabs = Array.from(this.tabStates.keys()).filter((id) => id !== tabId)
      if (remainingTabs.length > 0) {
        this.switchToTab(remainingTabs[remainingTabs.length - 1])
      } else {
        this.activeTabId = null
      }
    }

    // 移除视图
    if (state.isActive) {
      this.window.contentView.removeChildView(view)
    }

    const tabState = this.tabStates.get(tabId)

    // 清理session数据
    if (tabState && tabState.extra.isCreate) {
      view.webContents.session.clearData().catch(() => void 0)
      view.webContents.session.clearCache().catch(() => void 0)
    }

    // 清理协议处理器
    const partition = this.getPartitionForTab(state)
    this.unregisterProtocolHandler(view.webContents.session, partition)

    // 清理注入状态
    this.tabInjectReady.delete(tabId)

    // 销毁 WebContents
    if (!view.webContents.isDestroyed()) {
      view.webContents.stop()
      view.webContents.removeAllListeners()

      // @ts-expect-error 有, 但是没有导出
      view.webContents.destroy()
    }

    // 清理状态
    this.tabs.delete(tabId)
    this.tabStates.delete(tabId)

    // 更新剩余标签页的可关闭状态
    this.updateAllTabsCloseStatus()

    this.emit('tab-closed', tabId)
    this.broadcastTabsChanged()

    return true
  }

  /**
   * 导航到指定 URL
   */
  navigateTab(tabId: string, url: string): boolean {
    const view = this.tabs.get(tabId)
    const state = this.tabStates.get(tabId)

    if (!view || !state) {
      void this.createTab(url, { activate: true })
      return true
    }

    if (state.isNewTabPage && url !== 'about:blank') {
      state.isNewTabPage = false

      this.updateAllTabsCloseStatus()
    }

    const targetUrl = this.normalizeUrl(url)
    state.url = targetUrl
    state.domain = this.getDomain(state.url)
    this.broadcastTabsChanged()

    try {
      void view.webContents.loadURL(targetUrl).catch(noop)

      this.emit('navigation-started', tabId, targetUrl)

      if (tabId === this.activeTabId) {
        this.emit('navigation-changed', this.getNavigationInfo(tabId))
        this.broadcastNavigationChanged(tabId)
      }

      return true
    } catch (error) {
      logger.error('navigateTab Navigation failed:', error)
      return false
    }
  }

  /**
   * 后退
   */
  goBack(tabId?: string): boolean {
    const targetId = tabId || this.activeTabId
    if (!targetId) return false

    const view = this.tabs.get(targetId)
    if (!view || !view.webContents.navigationHistory.canGoBack()) {
      return false
    }

    view.webContents.navigationHistory.goBack()
    this.emit('navigation-action', targetId, 'back')
    return true
  }

  /**
   * 前进
   */
  goForward(tabId?: string): boolean {
    const targetId = tabId || this.activeTabId
    if (!targetId) return false

    const view = this.tabs.get(targetId)
    if (!view || !view.webContents.navigationHistory.canGoForward()) {
      return false
    }

    view.webContents.navigationHistory.goForward()
    this.emit('navigation-action', targetId, 'forward')
    return true
  }

  /**
   * 刷新
   */
  reload(tabId?: string, ignoreCache = false): boolean {
    const targetId = tabId || this.activeTabId
    if (!targetId) return false

    const view = this.tabs.get(targetId)
    const tabInfo = this.getTabInfo(targetId)
    if (!view || !tabInfo) return false

    let originalUrl: string | null = null
    try {
      const urlObj = new URL(tabInfo.url)
      const params = new URLSearchParams(urlObj.search)

      originalUrl = params.get('originalUrl')
    } catch (error) {
      logger.error('reload', error)
    }

    try {
      if (originalUrl) {
        void view.webContents.loadURL(originalUrl)
      } else {
        if (ignoreCache) {
          view.webContents.reloadIgnoringCache()
        } else {
          view.webContents.reload()
        }
      }

      this.emit('navigation-action', targetId, ignoreCache ? 'hard-reload' : 'reload')
      return true
    } catch (error) {
      logger.error('reload Reload failed:', error)
      return false
    }
  }

  /**
   * 停止加载
   */
  stop(tabId?: string): boolean {
    const targetId = tabId || this.activeTabId
    if (!targetId) return false

    const view = this.tabs.get(targetId)
    if (!view) return false

    view.webContents.stop()
    this.emit('navigation-action', targetId, 'stop')
    return true
  }

  /**
   * 获取所有标签页
   */
  getAllTabs(): TabInfo[] {
    return Array.from(this.tabStates.values())
  }

  /**
   * 获取活跃标签页
   */
  getActiveTab(): TabInfo | null {
    if (!this.activeTabId) return null
    return this.tabStates.get(this.activeTabId) || null
  }

  /**
   * 获取home标签页
   */
  getHomeTab(): TabInfo | null {
    if (!this.homeTabId) return null
    return this.tabStates.get(this.homeTabId) || null
  }

  /**
   * 获取活跃标签页ID
   */
  getActiveTabId(): string | null {
    return this.activeTabId
  }

  /**
   * 获取标签页信息
   */
  getTabInfo(tabId: string): TabInfo | null {
    return this.tabStates.get(tabId) || null
  }

  /**
   * 获取导航信息
   */
  getNavigationInfo(tabId?: string): NavigationInfo | null {
    const targetId = tabId || this.activeTabId
    if (!targetId) return null

    const view = this.tabs.get(targetId)
    const state = this.tabStates.get(targetId)

    if (!view || !state) return null

    return {
      url: state.url,
      title: state.title,
      canGoBack: view.webContents.navigationHistory.canGoBack(),
      canGoForward: view.webContents.navigationHistory.canGoForward(),
      isLoading: state.isLoading,
      progress: 0
    }
  }

  /**
   * 搜索或导航
   */
  searchOrNavigate(query: string, tabId?: string) {
    const targetId = tabId || this.activeTabId
    if (!targetId) return false

    const isUrl = this.isValidUrl(query)
    let targetUrl: string

    if (isUrl) {
      targetUrl = this.normalizeUrl(query)
    } else {
      targetUrl = `http://www.baidu.com/s?wd=${encodeURIComponent(query)}&cl=3&pn=0&rn=20`
    }

    return this.navigateTab(targetId, targetUrl)
  }

  /**
   * 向页面发送消息
   */
  sendMessageToTab(tabId: string, channel: string, data?: Placeholder): boolean {
    const view = this.tabs.get(tabId)
    if (!view) {
      logger.error(`sendMessageToTab Tab ${tabId} not found`)
      return false
    }

    try {
      view.webContents.send(channel, data)
      return true
    } catch (error) {
      logger.error('sendMessageToTab Failed to send message:', error)
      return false
    }
  }

  /**
   * 在页面中执行 JavaScript
   */
  async executeJavaScript(code: string, tabId?: string): Promise<Placeholder> {
    const targetId = tabId || this.activeTabId
    if (!targetId) throw new Error('No active tab')

    const view = this.tabs.get(targetId)
    if (!view) throw new Error(`Tab ${targetId} not found`)

    try {
      return await view.webContents.executeJavaScript(code)
    } catch (error) {
      logger.error('JavaScript execution failed:', error)
      throw error
    }
  }

  /**
   * 销毁所有标签页
   */
  destroy(): void {
    const tabIds = Array.from(this.tabs.keys())
    tabIds.forEach((tabId) => {
      try {
        // 强制关闭，忽略保护
        this.forceCloseTab(tabId)
      } catch {
        // 忽略
      }
    })

    // 清理所有协议注册
    this.registeredProtocolSessions.clear()

    this.activeTabId = null
    this.subscribers.clear()
  }

  /**
   * 强制关闭标签页（绕过保护）
   */
  private forceCloseTab(tabId: string): boolean {
    const view = this.tabs.get(tabId)
    const state = this.tabStates.get(tabId)

    if (!view || !state) return false

    // 移除视图
    if (state.isActive) {
      this.window.contentView.removeChildView(view)
    }

    // 清理协议处理器
    const partition = this.getPartitionForTab(state)
    this.unregisterProtocolHandler(view.webContents.session, partition)

    // 清理注入状态
    this.tabInjectReady.delete(tabId)

    // 销毁 WebContents
    if (!view.webContents.isDestroyed()) {
      view.webContents.stop()
      view.webContents.removeAllListeners()
      // @ts-expect-error 有, 但是没有导出
      view.webContents.destroy()
    }

    // 清理状态
    this.tabs.delete(tabId)
    this.tabStates.delete(tabId)

    return true
  }

  // 事件推送方法

  /**
   * 订阅事件推送
   */
  private subscribeToEvents(webContents: Electron.WebContents): void {
    this.subscribers.add(webContents)

    // 当 WebContents 被销毁时自动取消订阅
    webContents.once('destroyed', () => {
      this.subscribers.delete(webContents)
    })

    // 发送当前状态
    this.broadcastToSubscribers('tabs-state-changed', {
      tabs: this.getAllTabs(),
      activeTab: this.getActiveTab()
    })
  }

  /**
   * 取消订阅事件推送
   */
  private unsubscribeFromEvents(webContents: Electron.WebContents): void {
    this.subscribers.delete(webContents)
  }

  /**
   * 向所有订阅者广播事件
   */
  private broadcastToSubscribers(eventName: string, data?: Placeholder): void {
    this.subscribers.forEach((webContents) => {
      if (!webContents.isDestroyed()) {
        try {
          webContents.send(eventName, data)
        } catch (error) {
          logger.error('Failed to send _event to subscriber:', error)
          this.subscribers.delete(webContents)
        }
      } else {
        this.subscribers.delete(webContents)
      }
    })
  }

  /**
   * 广播标签页列表变化
   */
  private broadcastTabsChanged(): void {
    const tabsData = {
      tabs: this.getAllTabs(),
      activeTab: this.getActiveTab()
    }

    this.broadcastToSubscribers('tabs-state-changed', tabsData)
  }

  /**
   * 广播导航状态变化
   */
  private broadcastNavigationChanged(tabId?: string): void {
    const targetId = tabId || this.activeTabId
    if (!targetId) return

    const navigationInfo = this.getNavigationInfo(targetId)
    if (navigationInfo) {
      this.broadcastToSubscribers('navigation-state-changed', {
        tabId: targetId,
        ...navigationInfo
      })
    }
  }

  // 私有方法
  private setupTabEvents(tabId: string, view: WebContentsView): void {
    const state = this.tabStates.get(tabId)

    if (!state) return

    view.webContents.on('context-menu', (event) => {
      event.preventDefault()
    })

    view.webContents.on('did-stop-loading', () => {
      state.isLoading = false
      this.updateNavigationState(tabId)

      this.emit('loading-stopped', tabId)
      this.emit('tab-updated', state)
      this.broadcastTabsChanged()

      if (tabId === this.activeTabId) {
        this.emit('navigation-changed', this.getNavigationInfo(tabId))
        this.broadcastNavigationChanged(tabId)
      }
    })

    // 加载进度
    view.webContents.on('did-start-navigation', (_event, url, _isInPlace, isMainFrame) => {
      if (isMainFrame) {
        state.url = url

        this.emit('navigation-progress', tabId, 0)
      }
    })

    // 加载完成
    view.webContents.on('did-finish-load', () => {
      state.url = view.webContents.getURL()
      state.title = view.webContents.getTitle() || (state.isNewTabPage ? '新标签页' : '无标题')

      this.updateNavigationState(tabId)

      this.emit('loading-finished', tabId)
      this.emit('tab-updated', state)
      this.broadcastTabsChanged()

      if (tabId === this.activeTabId) {
        this.emit('navigation-changed', this.getNavigationInfo(tabId))
        this.broadcastNavigationChanged(tabId)
      }
    })

    // 加载失败
    view.webContents.on(
      'did-fail-load',
      (_event, errorCode, errorDescription, validatedURL, isMainFrame) => {
        if (isMainFrame) {
          this.updateNavigationState(tabId)

          this.emit('loading-failed', tabId, {
            errorCode,
            errorDescription,
            url: validatedURL
          })

          const errorParams = new URLSearchParams({
            errorCode: errorCode.toString(),
            errorDescription: errorDescription || '',
            originalUrl: validatedURL || '',
            timestamp: new Date().toISOString()
          })

          const errorPageUrl = `${errorPagePath}?${errorParams.toString()}`

          void view.webContents.loadURL(errorPageUrl)

          this.broadcastTabsChanged()
          if (tabId === this.activeTabId) {
            this.emit('navigation-changed', this.getNavigationInfo(tabId))
            this.broadcastNavigationChanged(tabId)
          }
        }
      }
    )

    // 标题更新
    view.webContents.on('page-title-updated', (_event, title) => {
      state.title = title || (state.isNewTabPage ? '新标签页' : '无标题')
      this.emit('tab-updated', state)
      this.broadcastTabsChanged()
    })

    // 图标更新
    view.webContents.on('page-favicon-updated', (_event, favicons) => {
      if (favicons.length > 0) {
        state.favicon = favicons[0]
        this.emit('tab-updated', state)
        this.broadcastTabsChanged()
      }
    })

    // 新窗口处理
    view.webContents.setWindowOpenHandler(({ url, disposition }) => {
      switch (disposition) {
        case 'new-window':
          // 打开新窗口
          return {
            action: 'allow',
            createWindow: (options) => {
              const browserWindow = new BrowserWindow({
                ...options,
                icon: getAssetPath('icon.png'),
                autoHideMenuBar: true
              })

              return browserWindow.webContents
            }
          }

        case 'default':
        case 'other':
        case 'foreground-tab':
        case 'background-tab':
          {
            const tabState = this.tabStates.get(tabId)

            // 在当前窗口创建新标签页
            void this.createTab(url, {
              ...tabState,
              isHome: false,
              activate: disposition === 'foreground-tab' || disposition === 'default'
            }).then((tabId) => {
              if (disposition === 'foreground-tab' || disposition === 'default') {
                this.switchToTab(tabId)
              }
            })
          }
          return { action: 'deny' }

        default:
          // 默认拒绝未知的打开方式
          return { action: 'deny' }
      }
    })

    // 导航事件
    view.webContents.on('will-navigate', (_event, navigationUrl) => {
      this.emit('will-navigate', tabId, navigationUrl)
    })

    // 域名变化
    view.webContents.on('did-navigate', (_event, url) => {
      this.emit('loading-started', tabId)
      state.isLoading = true
      state.url = url
      this.updateNavigationState(tabId)
      this.broadcastTabsChanged()

      if (tabId === this.activeTabId) {
        this.emit('navigation-changed', this.getNavigationInfo(tabId))
        this.broadcastNavigationChanged(tabId)
      }
    })
  }

  private setupWindowEvents(): void {
    this.window.on('resize', () => {
      if (this.activeTabId) {
        this.updateTabBounds(this.activeTabId)
      }
    })

    this.window.on('closed', () => {
      this.destroy()
    })
  }

  private async getAuthInfo() {
    const tab = this.activeTabId ? this.tabs.get(this.activeTabId) : null

    if (!tab) {
      logger.error(`getAuthInfo Tab with id ${this.activeTabId} not found`)
      return null
    }

    const tabInfo = this.activeTabId ? this.getTabInfo(this.activeTabId) : null
    if (!tabInfo) {
      logger.error(`getAuthInfo Tab info for tab ${this.activeTabId} not found`)
      return null
    }

    try {
      const cookies = await tab.webContents.session.cookies.get({})

      const localStorageData: StorageData = await tab.webContents.executeJavaScript(`
      (() => {
        const data = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            data[key] = localStorage.getItem(key);
          }
        }
        return data;
      })()
    `)

      const tabData = {
        cookies,
        localStorage: localStorageData,
        extra: tabInfo.extra
      }

      return tabData
    } catch (error) {
      logger.error(`Failed to extract data for tab ${this.activeTabId}:`, error)
      return null
    }
  }

  private async restoreCookies(
    tabId: string,
    savedData: { localStorage: StorageData; cookies: Cookie[] },
    url: string
  ): Promise<boolean> {
    const tab = this.tabs.get(tabId)
    if (!tab) {
      return false
    }

    try {
      if (savedData.cookies && Array.isArray(savedData.cookies)) {
        for (const cookie of savedData.cookies.map((cookie) => ({ url, ...cookie }))) {
          try {
            const cookieDomain = cookie.domain?.startsWith('.')
              ? cookie.domain.substring(1)
              : cookie.domain
            const protocol = cookie.secure ? 'https://' : 'http://'
            const cookieUrl = cookie.domain
              ? `${protocol}${cookieDomain}${cookie.path || '/'}`
              : url

            if (cookie.domain?.startsWith('.')) {
              await tab.webContents.session.cookies.set({
                ...cookie,
                url: cookieUrl
              })
            } else {
              const { domain: _domain, ...cookieWithoutDomain } = cookie
              await tab.webContents.session.cookies.set({
                ...cookieWithoutDomain,
                url: cookieUrl
              })
            }
          } catch (error) {
            logger.error('restoreCookies', error)
          }
        }
      }

      if (savedData.localStorage && typeof savedData.localStorage === 'object') {
        this.tabInjectReady.set(tabId, false)
        tab.webContents.session.webRequest.onResponseStarted(
          { urls: ['*://*/*'] },
          async (details) => {
            if (this.tabInjectReady.get(tabId)) {
              return
            }
            this.tabInjectReady.set(tabId, true)

            if (details.resourceType !== 'mainFrame') {
              return
            }

            const script = `
      (function() {
        try {
          const items = ${JSON.stringify(savedData.localStorage)};
          for (const key in items) {
            window.localStorage.setItem(key, items[key]);
          }
        } catch (e) {
          console.error('Failed to preload localStorage', e);
        }
      })();
    `

            await details.webContents?.executeJavaScript(script)
          }
        )
      }

      return true
    } catch (error) {
      logger.error(`restoreCookies Failed to restore data for tab ${tabId}:`, error)
      return false
    }
  }

  private setupIPC(): void {
    ipcMain.handle('page-manager:create-tab', (_event, url) => {
      return this.createTab(url, { activate: true })
    })

    ipcMain.handle('page-manager:save-cookies', async (_event) => {
      const tabInfo = this.activeTabId ? this.getTabInfo(this.activeTabId) : null

      if (!tabInfo) {
        logger.error(`page-manager:save-cookies Tab info for tab ${this.activeTabId} not found`)
        return false
      }

      if (tabInfo.extra && tabInfo.extra.platformName === '抖音') {
        await wait(10000)
      }

      const authInfo = await this.getAuthInfo()

      if (authInfo && authInfo.extra) {
        return saveAuth(authInfo.extra.openId, authInfo)
      }
    })

    ipcMain.handle('page-manager:create-new-tab-page', (_event) => {
      return this.createNewTabPage()
    })

    ipcMain.handle('page-manager:close-tab', (_event, tabId) => {
      return this.closeTab(tabId)
    })

    ipcMain.handle('page-manager:switch-tab', (_event, tabId) => {
      return this.switchToTab(tabId)
    })

    ipcMain.handle('page-manager:navigate', (_event, tabId, url) => {
      return this.navigateTab(tabId, url)
    })

    ipcMain.handle('page-manager:go-back', (_event, tabId) => {
      return this.goBack(tabId)
    })

    ipcMain.handle('page-manager:go-forward', (_event, tabId) => {
      return this.goForward(tabId)
    })

    ipcMain.handle('page-manager:reload', (_event, tabId, ignoreCache) => {
      return this.reload(tabId, ignoreCache)
    })

    ipcMain.handle('page-manager:get-all-tabs', () => {
      return this.getAllTabs()
    })

    ipcMain.handle('page-manager:get-active-tab', () => {
      return this.getActiveTab()
    })

    ipcMain.handle('page-manager:get-home-tab', () => {
      return this.getHomeTab()
    })

    ipcMain.handle('page-manager:search-or-navigate', (_event, query, tabId) => {
      return this.searchOrNavigate(query, tabId)
    })

    // 订阅事件推送
    ipcMain.handle('page-manager:subscribe-events', (_event) => {
      this.subscribeToEvents(_event.sender)
      return true
    })

    ipcMain.handle('page-manager:unsubscribe-events', (_event) => {
      this.unsubscribeFromEvents(_event.sender)
      return true
    })
  }

  public updateTabBoundAll() {
    const allTabs = Array.from(this.tabStates.values())
    allTabs.forEach((tab) => {
      this.updateTabBounds(tab.id)
    })
  }

  private updateTabBounds(tabId: string): void {
    const view = this.tabs.get(tabId)
    if (!view) return
    const tab = this.tabStates.get(tabId)
    if (!tab) return

    const bounds = this.window.getContentBounds()
    view.setBounds({
      x: 0,
      y: 80,
      width: bounds.width,
      height: bounds.height - 80
    })
  }

  private updateNavigationState(tabId: string): void {
    const view = this.tabs.get(tabId)
    const state = this.tabStates.get(tabId)

    if (!view || !state) return

    state.canGoBack = view.webContents.navigationHistory.canGoBack()
    state.canGoForward = view.webContents.navigationHistory.canGoForward()
  }

  private normalizeUrl(url: string): string {
    url = url.trim()

    if (url === '') {
      return 'about:blank'
    }

    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    if (url.startsWith('file://')) {
      return url
    }

    if (url.includes('.') && !url.includes(' ')) {
      return `https://${url}`
    }

    return url
  }

  private isValidUrl(string: string): boolean {
    try {
      const trimmed = string.trim()
      if (!trimmed) return false

      // 智能添加协议
      const urlString = /^https?:\/\//i.test(trimmed) ? trimmed : `https://${trimmed}`

      const url = new URL(urlString)

      // 检查是否有有效的主机名
      return url.hostname.length > 0 && (url.hostname.includes('.') || url.hostname === 'localhost')
    } catch (_) {
      return false
    }
  }

  private getDomain(string: string): string | null {
    try {
      const trimmed = string.trim()
      if (!trimmed) return null

      // 智能添加协议（与 isValidUrl 保持一致）
      const urlString = /^https?:\/\//i.test(trimmed) ? trimmed : `https://${trimmed}`

      const url = new URL(urlString)

      // 检查是否有有效的主机名（与 isValidUrl 的验证逻辑保持一致）
      if (url.hostname.length > 0 && (url.hostname.includes('.') || url.hostname === 'localhost')) {
        return url.hostname
      }

      return null
    } catch (_) {
      return null
    }
  }
}
