import { io, type Socket } from 'socket.io-client'
import { AppContext } from './context'
import { getStore } from './utils'
import { Logger } from './logger'
import type { CachedAccountInfo} from './task';
import { task, toPlatformAccountInfo } from './task'
import { ipcMain } from 'electron'
import { PlatformAccountInfo } from '@yixiaoer/platform-service/dist/cloud/model/BaseTaskFormModel';

const logger = Logger.getLogger('serverSocket')

export class ServerSocketManager {
  private socket: Socket
  private deviceId: string

  constructor(deviceId: string) {
    this.deviceId = deviceId

    this.socket = io(import.meta.env.VITE_APP_SOCKET, {
      path: '/socket.io',
      transports: ['websocket']
    })
    this.setupEventHandlers()
  }

  private updateServerState(state: boolean) {
    const webviewManager = AppContext.getInstance().getWebviewManager()
    if (webviewManager) {
      webviewManager.getNewTabPages().forEach((item) => {
        item.webContents.send('serverState', state)
      })
    }
  }

  private setupEventHandlers() {
    // 连接成功后注册设备
    this.socket.on('connect', () => {
      this.registerDevice()
    })

    this.socket.on('reconnect', () => {
      this.registerDevice()
    })

    this.socket.on('task:push', async (data, ack) => {
      import.meta.env.DEV && logger.info('收到推送消息', JSON.stringify(data))
      const state = await task
        .getPlatformService()
        .Cloud.pushOpenPlatformPublishTask(data.data, data.sessionToken)
      ack({ success: true, state })
    })

    this.socket.on('task:getPosition', async (data:{
      platform:CachedAccountInfo[]
       /**
     * 搜索关键词，根据关键词搜索相关poi地址，部分平台可空
     */
    keyWord: string;
    /**
     * 地址权限类型，0:默认值，不区分类型
     * 1:本地地址
     * 2:国内/全国地址
     * 3:海外地址
     */
    locationType?: number;
    /**
     * 下一页参数
     */
    nextPage?: string;
    }, ack) => {
      import.meta.env.DEV && logger.info('收到获取位置请求', JSON.stringify(data))
      try {
        const account = data.platform[0]
        const queryWorker = new (task.getPlatformService()).Cloud.QueryWorker(
          account.platformCookie,
          account.platformCode,
          toPlatformAccountInfo(account),
        )
        const positions = await queryWorker.searchLocation({
          keyWord: data.keyWord,
          locationType: data.locationType,
          nextPage: data.nextPage,
        })
        ack({ success: true, positions })
      } catch (error) {
        logger.error('获取位置列表失败', error)
        ack({ success: false, error: error instanceof Error ? error.message : '获取位置列表失败' })
      }
    })

    this.socket.on('update:reptile', () => {
      logger.info('收到更新推送消息:reptile')
      void AppContext.getInstance().getUpdaterManager().updateReptile()
    })

    this.socket.on('update:rpa', () => {
      logger.info('收到更新推送消息:rpa')
      void AppContext.getInstance().getUpdaterManager().updateRpa()
    })

    this.socket.on('update', () => {
      logger.info('收到更新推送消息:desktop')
      void AppContext.getInstance().getUpdaterManager().checkUpdate(false)
    })

    this.socket.on('disconnect', (_, description) => {
      logger.info('连接断开', description)
      this.updateServerState(false)
    })

    this.socket.on('connect_error', (err) => {
      logger.error('连接错误', err)
      this.updateServerState(false)
    })

    ipcMain.handle('serverSocket:getState', () => {
      return this.socket.connected
    })
  }

  // 注册设备
  private registerDevice() {
    this.updateServerState(true)
    this.socket.emit(
      'device:register',
      this.deviceId,
      (status: { success: false; deviceId: string }) => {
        if (status.success) {
          logger.info('设备注册成功', status.deviceId)
        }
      }
    )
  }


}

export function createServerSocketServer() {
  const serverSocketManager = new ServerSocketManager(getStore('deviceId'))

  AppContext.getInstance().setServerSocketManager(serverSocketManager)
}
