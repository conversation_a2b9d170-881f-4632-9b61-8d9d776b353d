import type { Socket } from 'socket.io'
import { Server } from 'socket.io'
import { dispatchMessages } from './business/message'
import { updateTrayMenu } from './menu'
import type http from 'http'
import { ipcMain } from 'electron'

export class SocketManager {
  private io: Server
  private clients: Map<string, { socketId: string; socket: Socket }> = new Map()

  constructor(server: http.Server) {
    this.io = new Server(server, {
      maxHttpBufferSize: 10 * 1024 * 1024
    })
    this.setupEventHandlers()
  }

  private updateClientState() {
    updateTrayMenu(this.clients.size === 0)
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: Socket) => {
      const { openId } = socket.handshake.auth

      this.clients.set(openId, { socketId: socket.id, socket })

      this.updateClientState()

      socket.on('message', ({ data, type }, callback) => {
        void dispatchMessages(openId, { type, data }, callback)
      })

      socket.on('disconnect', () => {
        this.clients.delete(openId)
        this.updateClientState()
      })
    })

    ipcMain.handle('client:getState', () => {
      return this.clients.size
    })
  }

  async sendToClient(openId: string, event: string, data: AnyObject) {
    const client = this.clients.get(openId)
    if (client) {
      return new Promise<boolean>((resolve) => {
        client.socket.timeout(5000).emit(event, data, (err: Error) => {
          if (err) {
            resolve(false)
          } else {
            resolve(true)
          }
        })
      })
    }

    return false
  }

  broadcast(event: string, data: Placeholder) {
    this.io.emit(event, data)
  }

  getConnectedClients(): string[] {
    return Array.from(this.clients.keys())
  }

  getClientCount() {
    return this.clients.size
  }

  isClientOnline(openId: string) {
    return this.clients.has(openId)
  }

  disconnectClient(openId: string) {
    const client = this.clients.get(openId)
    if (client) {
      client.socket.disconnect()
      return true
    }
    return false
  }
}
