import { isMac } from 'electron-global/platform'
import { join } from 'path'

export const onlinePagePath = import.meta.env.DEV
  ? `${import.meta.env.VITE_DEV_SERVER_URL}online.html`
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/online.html')
    ).toString()

export const toolPagePath = import.meta.env.DEV
  ? `${import.meta.env.VITE_DEV_SERVER_URL}tool.html`
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/tool.html')
    ).toString()

export const updatePagePath = import.meta.env.DEV
  ? `${import.meta.env.VITE_DEV_SERVER_URL}update.html`
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/update.html')
    ).toString()

/**
 * main 页面地址
 */
export const mainPagePath = import.meta.env.DEV
  ? import.meta.env.VITE_DEV_SERVER_URL
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/index.html')
    ).toString()
/**
 * 媒体预览页面
 */
export const placeholderPagePath = import.meta.env.DEV
  ? `${import.meta.env.VITE_DEV_SERVER_URL}placeholder.html`
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/placeholder.html')
    ).toString()

export const errorPagePath = import.meta.env.DEV
  ? `${import.meta.env.VITE_DEV_SERVER_URL}error.html`
  : new URL(
      join(isMac ? 'file:///' : '', __dirname, '..', '..', '/renderer/dist/error.html')
    ).toString()
