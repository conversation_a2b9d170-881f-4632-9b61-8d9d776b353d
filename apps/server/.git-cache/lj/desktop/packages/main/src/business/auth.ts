import { task } from '../task'
import { AppContext } from '../context'
import { showWindow } from '../menu'

export async function saveAuth(
  openId: string,
  {
    extra,
    cookies,
    localStorage
  }: { cookies: Electron.Cookie[]; localStorage: AnyObject; extra: AnyObject }
) {
  const { platformName, accountId } = extra

  if (!platformName) {
    const { color, spaceName, unsaved, url } = extra
    return AppContext.getInstance()
      .getSocketManager()
      .sendToClient(openId, 'message', {
        type: extra.type,
        data: JSON.stringify({
          url,
          cookies,
          localStorage,
          accountId,
          color,
          spaceName,
          unsaved
        })
      })
  }

  const accountService = new (task.getPlatformService().AccountService)(
    JSON.stringify(cookies),
    platformName
  )

  const [accountState, accountInfo] = await Promise.all([
    accountService.checkAccountAlive().catch(() => 0),
    task
      .getPlatformService()
      .Cloud.getPlatformUserInfo(platformName, JSON.stringify(cookies))
      .catch(() => null)
  ])

  return AppContext.getInstance()
    .getSocketManager()
    .sendToClient(openId, 'message', {
      type: extra.type,
      data: JSON.stringify({
        cookies,
        localStorage,
        accountState,
        accountId,
        accountInfo: {
          platformName,
          accountId: accountInfo?.yixiaoerId,
          nickName: accountInfo?.yixiaoerName,
          avatar: accountInfo?.yixiaoerImageUrl,
          identityVerified: accountInfo?.verify
        }
      })
    })
}

export async function createAuthContextView({
  url,
  openId,
  type,
  accountId,
  color,
  spaceName,
  unsaved
}: {
  url: string
  accountId: string
  color: string
  spaceName: string
  unsaved: boolean
  openId: string
  type: string
}) {
  const appContext = AppContext.getInstance()
  await appContext.getWebviewManager().createAuthTabPage(url, {
    openId,
    type,
    isCreate: true,
    accountId,
    color,
    spaceName,
    unsaved,
    url
  })
  showWindow(appContext.getWindow())
}

export async function createAuthView({
  platformName,
  openId,
  type,
  accountId,
  cookies,
  localStorage
}: {
  cookies?: string
  localStorage?: string
  platformName: string
  openId: string
  type: string
  accountId?: string
}) {
  const platform = task.getPlatformService().injection.platformConfig[platformName]

  if (!platform) {
    return
  }

  const appContext = AppContext.getInstance()

  if ((cookies || localStorage) && accountId) {
    openAuthView({
      url: platform.authorizeUrl,
      title: platformName,
      openId,
      platformName,
      accountId,
      cookies: cookies || '[]',
      localStorage: localStorage || '{}'
    })
    showWindow(appContext.getWindow())
    return
  }

  await appContext.getWebviewManager().createAuthTabPage(platform.authorizeUrl, {
    openId,
    type,
    platformName,
    isCreate: true,
    accountId
  })
  showWindow(appContext.getWindow())
}

export async function openView({
  url,
  color,
  accountId,
  spaceName
}: {
  url: string
  color: string
  accountId: string
  spaceName: string
}) {
  const appContext = AppContext.getInstance()
  await appContext.getWebviewManager().createTab(url, {
    activate: true,
    extra: {
      color,
      accountId,
      spaceName
    }
  })
  showWindow(appContext.getWindow())
}

export function openAuthView({
  url,
  title,
  accountId,
  openId,
  platformName,
  cookies,
  localStorage,
  isUpdateAuth
}: {
  url: string
  title: string
  openId: string
  platformName: string
  accountId: string
  cookies: string
  localStorage: string
  isUpdateAuth?: boolean
}) {
  const appContext = AppContext.getInstance()
  const _cookies = JSON.parse(cookies) as Electron.Cookie[]
  const _localStorage = JSON.parse(localStorage) as AnyObject
  void appContext.getWebviewManager().createRestoreAuthTabPage(url, _cookies, _localStorage, {
    accountId,
    spaceName: title,
    platformName,
    openId,
    isUpdateAuth,
    type: 'open-auth-view'
  })
  showWindow(appContext.getWindow())
}
