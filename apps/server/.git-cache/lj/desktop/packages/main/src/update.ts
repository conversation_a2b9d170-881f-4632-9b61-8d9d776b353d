import type { ProgressInfo, UpdateInfo, UpdaterEvents } from 'electron-updater'
import { autoUpdater } from 'electron-updater'
import { app, type BrowserWindow, ipcMain, type WebContents } from 'electron'
import { type DownloadNotification } from 'electron-updater/out/AppUpdater'
import { Logger } from './logger'
import axios from 'axios'
import { isWin } from 'electron-global/platform'
// import { createUpdateWindow } from './createUpdateWindow'
// import { showWindow } from './menu'
import { getStore, setStore } from './utils'
import fs from 'fs-extra'
import { reptileFilePath } from './constant'
import { AppContext } from './context'

const logger = Logger.getLogger('update')

if (import.meta.env.DEV) {
  autoUpdater.forceDevUpdateConfig = true
  // 或者设置环境变量
  process.env.ELECTRON_IS_DEV = '0'
}

interface UpdaterState {
  isQuitting: boolean
  isCheckingUpdate: boolean
  webContents: WebContents | null
  autoQuitAndInstall: boolean
  description?: string
}

export interface UpdateConfig {
  description?: string
  autoDownload: boolean
  autoQuitAndInstall: boolean
  feedUrl?: string
}

export class UpdaterManager {
  private readonly channel: string

  private window: BrowserWindow | null = null
  private rootWindow: BrowserWindow
  private state: UpdaterState = {
    isQuitting: false,
    isCheckingUpdate: false,
    webContents: null,
    autoQuitAndInstall: false
  }

  constructor(rootWindow: BrowserWindow) {
    this.rootWindow = rootWindow
    this.channel = `${process.platform}-${process.arch}`
    this.initializeUpdater()
    this.setupEventListeners()
  }

  private setIsQuitting(value: boolean): void {
    this.state.isQuitting = value
  }

  private setDescription(description: string): void {
    this.state.description = description
  }

  private setIsCheckingUpdate(value: boolean): void {
    this.state.isCheckingUpdate = value
  }

  private setAutoQuitAndInstall(value: boolean): void {
    this.state.autoQuitAndInstall = value
  }

  private getAutoQuitAndInstall(): boolean {
    return this.state.autoQuitAndInstall
  }

  private initializeUpdater(): void {
    autoUpdater.channel = this.channel
    void this.updateRpa()
    void this.updateReptile()
    logger.info('autoUpdater.channel', this.channel)
  }

  public checkUpdate(isAllowShow: boolean = true) {
    logger.log(`${isAllowShow}`)
    // const res = await axios.get<{
    //   hasUpdate: boolean
    //   latestVersion: { downloadUrl: string; description: string }
    //   forceUpdate: boolean
    // }>(`${import.meta.env.VITE_APP_API}/open/verson/checkForUpdate`, {
    //   params: {
    //     type: 'DESKTOP',
    //     platform: isWin ? 'WIN' : 'MAC',
    //     version: __APP_VERSION__
    //   }
    // })
    // if (isAllowShow) {
    //   if (!this.window || (this.window && this.window.isDestroyed())) {
    //     this.window = await createUpdateWindow(this.rootWindow)
    //   } else {
    //     showWindow(this.window)
    //     return
    //   }
    // } else {
    //   if (res.data.hasUpdate) {
    //     if (!this.window || (this.window && this.window.isDestroyed())) {
    //       this.window = await createUpdateWindow(this.rootWindow)
    //     } else {
    //       showWindow(this.window)
    //       return
    //     }
    //   } else {
    //     return
    //   }
    // }
    // void this.checkForUpdatesAndNotify({
    //   autoDownload: res.data.forceUpdate,
    //   autoQuitAndInstall: res.data.forceUpdate,
    //   feedUrl: res.data.latestVersion.downloadUrl,
    //   description: res.data.latestVersion.description
    // })
  }

  private setupEventListeners(): void {
    autoUpdater.on('checking-for-update', () => {
      this.sendStatusToWindow('checking-for-update')
    })

    autoUpdater.on('update-available', (info) => {
      this.sendStatusToWindow('update-available', info)
    })

    autoUpdater.on('update-not-available', (info) => {
      this.sendStatusToWindow('update-not-available', info)
    })

    autoUpdater.on('error', (err) => {
      this.sendStatusToWindow('error', err)
    })

    autoUpdater.on('download-progress', (progressObj) => {
      this.sendStatusToWindow('download-progress', progressObj)
    })

    autoUpdater.on('update-downloaded', (info) => {
      if (this.getAutoQuitAndInstall()) {
        this.quitAndInstall()
      }
      this.sendStatusToWindow('update-downloaded', info)
    })

    ipcMain.handle('checkUpdate', () => {
      void this.checkUpdate()
    })

    ipcMain.handle(
      'autoUpdate',
      (
        _event,
        {
          type
        }: {
          type: 'checkUpdate' | 'downloadUpdate' | 'quitAndInstall'
        }
      ) => {
        switch (type) {
          case 'downloadUpdate':
            void this.downloadUpdate()
            break
          case 'quitAndInstall':
            this.quitAndInstall()
            break
          default:
            break
        }
      }
    )
  }

  public async updateRpa() {
    let rpaVersion = getStore('rpaVersion') as string | null

    if (!rpaVersion) {
      rpaVersion = '0.0.0'
    }

    const res = await axios.get<{
      hasUpdate: boolean
      latestVersion: { downloadUrl: string; description: string; version: string }
      forceUpdate: boolean
    }>(`${import.meta.env.VITE_APP_API}/open/verson/checkForUpdate`, {
      params: {
        type: 'RPA',
        platform: isWin ? 'WIN' : 'MAC',
        version: rpaVersion
      }
    })

    if (res.data.hasUpdate) {
      const data = await axios.get(res.data.latestVersion.downloadUrl, {
        responseType: 'arraybuffer'
      })
      setStore('rpaVersion', res.data.latestVersion.version)
      setStore('rapData', data.data.toString())
    }
  }

  public async updateReptile() {
    let reptileVersion = getStore('reptileVersion') as string | null

    if (!reptileVersion) {
      reptileVersion = '0.0.0'
    }

    const res = await axios.get<{
      hasUpdate: boolean
      latestVersion: { downloadUrl: string; description: string; version: string }
      forceUpdate: boolean
    }>(`${import.meta.env.VITE_APP_API}/open/verson/checkForUpdate`, {
      params: {
        type: 'CRAWLER',
        platform: isWin ? 'WIN' : 'MAC',
        version: reptileVersion
      }
    })

    if (res.data.hasUpdate) {
      const data = await axios.get(res.data.latestVersion.downloadUrl, {
        responseType: 'arraybuffer'
      })
      setStore('reptileVersion', res.data.latestVersion.version)
      fs.outputFileSync(reptileFilePath, data.data)
    }
  }

  private sendStatusToWindow(event: UpdaterEvents, info?: UpdateInfo | Error | ProgressInfo): void {
    this.window?.webContents.send('autoUpdate', {
      event,
      data: info,
      autoDownload: autoUpdater.autoDownload,
      description: this.state.description
    })

    // 更新检查状态
    if (this.isCheckCompleteEvent(event)) {
      this.setIsCheckingUpdate(false)
    }
  }

  private isCheckCompleteEvent(event: UpdaterEvents): boolean {
    return ['checking-for-update', 'update-not-available', 'update-available', 'error'].includes(
      event
    )
  }

  private configureFeedUrl(url: string): void {
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: url,
      channel: this.channel
    })
  }

  // 公共方法
  async checkForUpdatesAndNotify(
    config: UpdateConfig,
    notification?: DownloadNotification
  ): Promise<void> {
    if (this.state.isCheckingUpdate) {
      logger.info('已在检查更新中，跳过本次请求')
      return
    }

    // 配置更新源
    if (config.feedUrl) {
      this.configureFeedUrl(config.feedUrl)
    }

    if (config.description) {
      this.setDescription(config.description)
    }

    // 设置状态
    this.setIsCheckingUpdate(true)
    this.setAutoQuitAndInstall(config.autoQuitAndInstall)

    // 配置自动下载
    autoUpdater.autoDownload = config.autoDownload

    // 默认通知配置
    const defaultNotification: DownloadNotification = {
      title: '{appName} 发现新的版本',
      body: '{version} 版本已准备就绪，重启后可自动完成更新。'
    }

    try {
      await autoUpdater.checkForUpdatesAndNotify(notification || defaultNotification)
    } catch (error) {
      logger.error('检查更新失败:', error)
      this.setIsCheckingUpdate(false)
      throw error
    }
  }

  quitAndInstall(): void {
    this.setIsQuitting(true)
    if (isWin) {
      autoUpdater.quitAndInstall(true, true)
    } else {
      setImmediate(() => {
        app.removeAllListeners('window-all-closed')

        AppContext.getInstance().getMainWindow()?.destroy()

        autoUpdater.quitAndInstall()
      })
    }
  }

  async downloadUpdate(): Promise<void> {
    try {
      await autoUpdater.downloadUpdate()
    } catch (error) {
      logger.error('下载更新失败:', error)
      throw error
    }
  }

  // 公共 Getter 方法
  getIsQuitting(): boolean {
    return this.state.isQuitting
  }

  getIsCheckingUpdate(): boolean {
    return this.state.isCheckingUpdate
  }

  getChannel(): string {
    return this.channel
  }
}
