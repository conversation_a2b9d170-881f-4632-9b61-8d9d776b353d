import { app, BrowserWindow, screen } from 'electron'
import { instruct } from './instruct'
import { nativeTheme } from 'electron'
import { isWin } from 'electron-global/platform'
import { IpcChannelEnum } from 'electron-global/enum'
import { updateTrayMenu } from './menu'
import { wait } from 'electron-global/wait'
export const primaryWindowMaps: Record<number, 1> = {}

let isQuiting = false

app.on('before-quit', () => {
  isQuiting = true
})

export async function createWindow({
  path,
  minHeight,
  minWidth,
  height,
  width,
  isHideReplaceClose,
  resizable,
  closeCallback,
  maximizable,
  ...rest
}: {
  minHeight: number
  minWidth: number
  path: string
  height: number
  width: number
  isHideReplaceClose: boolean
  resizable: boolean
  maximizable: boolean
  closeCallback?: () => void
} & Electron.BrowserWindowConstructorOptions) {
  const cursorPoint = screen.getCursorScreenPoint()
  const targetDisplay = screen.getDisplayNearestPoint(cursorPoint)
  const centerX = targetDisplay.bounds.x + targetDisplay.bounds.width / 2
  const centerY = targetDisplay.bounds.y + targetDisplay.bounds.height / 2
  const winX = centerX - width / 2
  const winY = centerY - height / 2

  nativeTheme.themeSource = 'light'

  const browserWindow = new BrowserWindow({
    trafficLightPosition: {
      x: 10,
      y: 10
    },
    x: Math.round(winX),
    y: Math.round(winY),
    resizable,
    maximizable,
    minHeight,
    minWidth,
    height,
    width,
    backgroundColor: '#ffffff',
    show: false,
    ...rest
  })

  browserWindow.once('ready-to-show', () => {
    browserWindow.show()
  })

  if (import.meta.env.DEV) {
    browserWindow.webContents.on('context-menu', (event) => {
      event.preventDefault()
    })
  }

  if (isWin) {
    browserWindow.hookWindowMessage(0x0116, () => {
      browserWindow.setEnabled(false)
      browserWindow.setEnabled(true)
    })

    browserWindow.on('maximize', () => {
      browserWindow.webContents.send(IpcChannelEnum.WindowMaximize, true)
    })

    browserWindow.on('unmaximize', () => {
      browserWindow.webContents.send(IpcChannelEnum.WindowMaximize, false)
    })
  }

  browserWindow.webContents.on('render-process-gone', (_) => {
    browserWindow.reload()
  })

  if (isHideReplaceClose) {
    browserWindow.on('close', async (event) => {
      if (!isQuiting) {
        event.preventDefault()
        closeCallback?.()
        await wait(100)
        browserWindow.hide()
      }
    })
  } else {
    browserWindow.on('close', () => {
      delete primaryWindowMaps[browserWindow.webContents.id]
      instruct.deleteValue(browserWindow.webContents.id)
      updateTrayMenu()
    })
  }

  /**
   * 主窗口的网址。
   * 用于开发的 Vite 开发服务器。
   * `file://../renderer/index.html` 用于生产和测试的。
   */
  await browserWindow.loadURL(path)

  instruct.initializationValue(browserWindow.webContents.id, browserWindow)

  primaryWindowMaps[browserWindow.id] = 1
  return browserWindow
}
