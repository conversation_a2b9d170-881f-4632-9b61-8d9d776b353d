const { loadEnv } = require('vite')
const path = require('path')
const fs = require('fs-extra')
const package = require('../../package.json')

const env = loadEnv(process.env.NODE_ENV, path.join(__dirname, '..', '..'), 'VITE_')

console.log(`📦 开始打包主进程`)
console.log(`📦 当前版本: ${package.version}`)
console.log(`📦 当前环境: ${process.env.NODE_ENV}`)
console.log(`📦 当前API: ${env.VITE_APP_API}`)
console.log(`📦 当前SOCKET: ${env.VITE_APP_SOCKET}`)

fs.ensureDirSync(path.join(__dirname, 'dist'))
fs.copySync(path.join(__dirname, 'assets'), path.join(__dirname, 'dist'))

const cachePath = path.join(__dirname, '.cache')

require('@vercel/ncc')(path.join(__dirname, 'src', 'index.ts'), {
  externals: ['electron'],
  filterAssetBase: process.cwd(),
  minify: false,
  sourceMap: false,
  cache: cachePath,
  assetBuilds: false
}).then(({ code, assets }) => {
  const outPath = path.join(__dirname, 'dist', 'index.cjs')
  let _code = code
    .replace(/new Buffer\(/g, 'Buffer.from(')
    .replace(/import\.meta\.env\.DEV/g, 'false')
    .replace(/import\.meta\.env\.VITE_DEV_SERVER_URL/g, '""')
    .replace(/__APP_VERSION__/g, JSON.stringify(package.version))
    .replace(/__IS_PROD__/g, `${process.env.NODE_ENV === 'production'}`)

  Object.keys(env).forEach((key) => {
    const envVar = `import\\.meta\\.env\\.${key}`
    const value = JSON.stringify(env[key])

    _code = _code.replace(new RegExp(envVar, 'g'), value)
  })

  fs.outputFileSync(outPath, _code)

  if (assets) {
    Object.keys(assets).forEach((fileName) => {
      fs.outputFileSync(path.join(__dirname, 'dist', fileName), assets[fileName].source)
    })
  }
})
