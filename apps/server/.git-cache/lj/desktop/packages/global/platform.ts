export const EPlatform = <const>{
  Win: 'win',
  Mac: 'mac',
  Linux: 'linux'
}

export const Platform = (() => {
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // 在 Node.js 中运行
    if (process.platform === 'win32') return EPlatform.Win
    if (process.platform === 'darwin') return EPlatform.Mac
    if (process.platform === 'linux') return EPlatform.Linux
  }

  // 在浏览器环境中
  const ua = navigator.userAgent.toLowerCase()
  if (/windows/.test(ua)) return EPlatform.Win
  if (/mac/.test(ua)) return EPlatform.Mac
  if (/linux/.test(ua)) return EPlatform.Linux
})() as (typeof EPlatform)[keyof typeof EPlatform]

/**
 * 是否是windows
 */
export const isWin = (() => {
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // 在 Node.js 中运行
    return process.platform === 'win32'
  }
  return /windows/i.test(navigator.userAgent.toLowerCase())
})()

/**
 * 是否是mac
 */
export const isMac = (() => {
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // 在 Node.js 中运行
    return process.platform === 'darwin'
  }
  return /macintosh|mac os x/i.test(navigator.userAgent.toLowerCase())
})()

/**
 * 是否是linux
 */
export const isLinux = (() => {
  if (typeof window === 'undefined' && typeof process !== 'undefined') {
    // 在 Node.js 中运行
    return process.platform === 'linux'
  }
  return /linux/i.test(navigator.userAgent.toLowerCase())
})()
