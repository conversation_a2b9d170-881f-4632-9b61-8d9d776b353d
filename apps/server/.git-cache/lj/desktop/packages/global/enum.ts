/**
 * 更新相关
 */
export enum ChannelEnum {
  shortcutKeyRegisterFailed = 'SHORTCUTKEYREGISTERFAILED',
  autoUpdate = 'AUTOUPDATE'
}

/**
 * 子窗口
 */
export enum WindowEnum {
  Voice = 'voice',
  Preview = 'preview',
  Forward = 'forward'
}

/**
 * ipc 通道
 */
export enum IpcChannelEnum {
  WindowMaximize = 'ipc-window-max-and-min',
  /**
   * 公共信道
   */
  Public = 'ipc-public',

  /**
   * 截图回调信道
   */
  RenderScreenshotsSrouce = 'render-screenshots-source',

  /**
   * 通知信道
   */
  Notice = 'ipc-notice',

  /**
   * 通知点击信道
   */
  NoticeClick = 'ipc-notice-click',

  /**
   * 通知渲染信道
   */
  NoticeRender = 'ipc-notice-render'
}

/**
 * window action
 */
export enum WindowsInstructEnum {
  /**
   * 窗口最小化
   */
  WindowMin = 'windowsMin',
  /**
   * 窗口最大化
   */
  WindowMax = 'windowsMax',
  /**
   * 关闭窗口
   */
  WindowClose = 'windowsClose'
}

/**
 * 杂项
 */
export enum InstructEnum {
  CheckUpdate,
  GetToolPath,
  /**
   * 显示更多菜单
   */
  ShowMoreMenu,

  Hide,
  /**
   * 退出
   */
  Logout,
  /**
   * 登录
   */
  Login,
  /**
   * 打开目录
   */
  OpenDir,
  /**
   * 打开链接
   */
  OpenExternal,
  /**
   * 日志
   */
  Logger,
  /**
   * 设置主题
   */
  SetTheme,
  /**
   * 退出
   */
  Quit,
  /**
   * 原生弹窗
   */
  NativeDialog,
  /**
   * 通知
   */
  MacosSendNotification,
  /**
   * 是否聚焦
   */
  isFocus,
  /**
   * 保存文件
   */
  SaveAsFile,
  /**
   * 注册显示窗口
   */
  RegistrationShortcutDisplayWindow,
  /**
   * 打开链接
   */
  SystemPreferences,
  /**
   * 更新应用
   */
  UpdateVersion,
  /**
   * 更新菜单
   */
  UpdateAppMenu,
  /**
   * 获取登录信息
   */
  GetUserInfo,
  /**
   * 保存登录信息
   */
  SaveUserInfo,
  /**
   * 创建一个新的主窗口
   */
  CreateMainWindow
}
