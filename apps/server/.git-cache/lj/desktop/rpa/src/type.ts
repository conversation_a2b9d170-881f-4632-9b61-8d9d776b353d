export interface VideoTask {
  videoName: string
  videoMime: string
  platformName: string
  accountId: string
  title: string
  description: string
  tags: string[]
  topic: string[]
  videoPath: string
  videoCover: string
  isOriginal: boolean
  locationKeyword: string
  categories: Record<string, string>[]
}

export type IVideoTask = Omit<VideoTask, 'platformName'> & {
  url: string
  tabId: string
  videoName: string
  videoMime: string
}

export interface ImageTask {
  accountId: string
  platformName: string
  cover: string
  title: string
  images: { path: string; format: string }[]
  description: string
  locationKeyword: string
  music: string
}

export type IImageTask = Omit<ImageTask, 'platformName'> & {
  images: { path: string; format: string }[]
  url: string
  tabId: string
  topic: string[]
}
