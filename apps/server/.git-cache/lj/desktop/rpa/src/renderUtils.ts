import type { JSX } from 'react'

export const retryCount = 5

export function manualDispatchFileEvent({
  dom,
  element,
  elementKey,
  event,
  value
}: {
  dom: HTMLElement | null
  element: typeof HTMLElement
  elementKey: string
  value: Placeholder
  event: string
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey)
  if (proto && proto.set && dom) {
    proto.set.call(dom, value)
    dom.dispatchEvent(new Event(event, { bubbles: true }))
  }
}

export function uint8ArrayToFile(uint8Array: Uint8Array, fileName: string, mimeType: string) {
  const blob = new Blob([uint8Array], { type: mimeType })
  const file = new File([blob], fileName, { type: mimeType })

  return file
}

export function wait(time = 1000) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

export function getFileBuffer(filePath: string) {
  return window.app.ipcRenderer.invoke('read-file-buffer', filePath) as Promise<Uint8Array>
}

export function onSendInput(dom: HTMLElement, value: string, tabId: string) {
  dom.focus()
  return window.app.ipcRenderer.invoke('rpa-send-event', value, tabId)
}

export function onSendEntryInput(dom: HTMLElement, tabId: string) {
  dom.focus()
  return window.app.ipcRenderer.invoke('rpa-send-entry-event', tabId)
}

export function baseStartPush() {
  const start = getElementByText('button', '发布')
  if (start && start instanceof HTMLButtonElement) {
    start.click()
    return true
  }

  const start5 = getElementByText('button', '发表')
  if (start5 && start5 instanceof HTMLButtonElement) {
    start5.click()
    return true
  }

  const start1 = getElementByText('div', '发布')
  if (start1 && start1 instanceof HTMLDivElement) {
    start1.click()
    return true
  }

  const start2 = getElementByText('span', '发 布')
  if (start2 && start2 instanceof HTMLSpanElement) {
    start2.click()
    return true
  }

  const start3 = getElementByText('span', '发布')
  if (start3 && start3 instanceof HTMLSpanElement) {
    start3.click()
    return true
  }

  const start4 = getElementByText('span', '立即投稿')
  if (start4 && start4 instanceof HTMLSpanElement) {
    start4.click()
    return true
  }

  return false
}

export function getElementByText(
  dom: keyof JSX.IntrinsicElements,
  text: string,
  container: HTMLElement | Document = document
) {
  const xpathResult = document.evaluate(
    `//${dom}[text()='${text}']`,
    container,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  )

  try {
    return xpathResult.singleNodeValue
  } catch {
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
      const node = xpathResult.snapshotItem(i)
      if (node) {
        return node
      }
    }
  }
}

export function getFileExtension(filename: string) {
  return filename.substring(filename.lastIndexOf('.') + 1)
}

export async function retry<T>(fn: () => Promise<T>, maxRetries: number = 5): Promise<T> {
  let attempts = 0

  while (attempts < maxRetries) {
    try {
      const result = await fn()
      if (result) {
        return
      }
    } catch {
      /* empty */
    }

    attempts++
    await wait(1000)
  }
}
