import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  description: 0,
  tags: 0,
  categories: 0
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.container-section.video-first input[name="video"]')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        renderTaskMap.video = retryCount
        await wait(3000)
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.description < retryCount) {
    const wapperDom = document.querySelector('.abstract-main-textarea')
    if (wapperDom && wapperDom instanceof HTMLTextAreaElement) {
      manualDispatchFileEvent({
        dom: wapperDom,
        element: HTMLTextAreaElement,
        elementKey: 'value',
        value: config.description,
        event: 'change'
      })
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  if (renderTaskMap.tags < retryCount) {
    if (config.tags.length) {
      const inputDom = document.querySelector('.el-select__tags .el-select__input')
      if (inputDom && inputDom instanceof HTMLInputElement) {
        for (const item of config.tags) {
          await onSendInput(inputDom, item, config.tabId)
          await wait(500)
          await onSendEntryInput(inputDom, config.tabId)
          await wait(500)
        }
        renderTaskMap.tags = retryCount
      }
    } else {
      renderTaskMap.tags = retryCount
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const [a, b] = config.categories
      const ali = getElementByText('li', a.text)
      if (ali && ali instanceof HTMLLIElement) {
        ali.click()
        await wait(200)
      }

      const bili = getElementByText('li', b.text)
      if (bili && bili instanceof HTMLLIElement) {
        bili.click()
        renderTaskMap.categories = retryCount
      }
    } else {
      renderTaskMap.categories = retryCount
    }

    renderTaskMap.categories++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
