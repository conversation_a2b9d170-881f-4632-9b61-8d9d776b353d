import type { IImageTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  image: 0,
  description: 0,
}

async function renderImage(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    const inputDom = document.querySelector(
      '#homeWrap input[accept="image/*, .jpg, .jpeg, .bmp, .gif, .png, .heif, .heic, video/mp4,video/x-m4v,video/*,.mkv,.flv"][multiple]',
    )

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const files = new DataTransfer()

      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path)
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`,
          )

          files.items.add(file)
        }
      }

      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: 'files',
        value: files.files,
        event: 'change',
      })

      renderTaskMap.image = retryCount
      await wait(3000)
    }

    renderTaskMap.image++
  }
}

async function render(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    return
  }

  if (renderTaskMap.description < retryCount) {
    const textareaDom = document.querySelector('#homeWrap textarea[class^="Form_input_"]')

    if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
      await onSendInput(textareaDom, config.description, config.tabId)

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}
