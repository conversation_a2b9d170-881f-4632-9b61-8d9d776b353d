import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  onSendInput,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = () => {
  const buttonSpan = getElementByText('span', '发布')

  if (
    buttonSpan &&
    buttonSpan.parentNode &&
    buttonSpan.parentNode.parentNode &&
    buttonSpan.parentNode.parentNode instanceof HTMLButtonElement
  ) {
    buttonSpan.parentNode.parentNode.click()
    return true
  }

  return baseStartPush()
}

const renderTaskMap = {
  video: false,
  title: false,
  description: false,
  // local: false,
  original: false
}

async function renderVideo(config: IVideoTask) {
  if (!renderTaskMap.video) {
    const dom = document.querySelector('[id^="area_video_button_upload_"]')

    if (dom && dom instanceof HTMLDivElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const file = uint8ArrayToFile(uint8Array, config.videoName, config.videoMime)
        const files = new DataTransfer()
        files.items.add(file)

        const dropEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          dataTransfer: files
        })

        dom.dispatchEvent(dropEvent)

        renderTaskMap.video = true
      }
    }
  }
}

async function render(config: IVideoTask) {
  if (!renderTaskMap.title) {
    if (!config.title) {
      renderTaskMap.title = true
    } else {
      const titleDom = document.querySelector('input[placeholder="填写标题（0～30个字）"]')

      if (titleDom && titleDom instanceof HTMLInputElement) {
        titleDom.value = config.title
        titleDom.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }))
        titleDom.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }))
        renderTaskMap.title = true
      }
    }
  }

  if (!renderTaskMap.description) {
    if (!config.description) {
      renderTaskMap.description = true
    } else {
      const descDom = document.querySelector('textarea[placeholder="有什么新鲜事想分享给大家？"]')

      if (descDom && descDom instanceof HTMLTextAreaElement) {
        await onSendInput(descDom, config.description, config.tabId)
        renderTaskMap.description = true
      }
    }

    if (!renderTaskMap.original) {
      const originalDoms = document.querySelectorAll('.woo-radio-main')
      if (!config.isOriginal) {
        const originalDom = originalDoms[1]

        if (originalDom && originalDom instanceof HTMLLabelElement) {
          originalDom.click()
        }
        renderTaskMap.original = true
      } else {
        const originalDom = originalDoms[0]
        if (originalDom && originalDom instanceof HTMLLabelElement) {
          originalDom.click()

          renderTaskMap.original = true
        }
      }
    }

    // if (
    //   !renderTaskMap.local &&
    //   renderTaskMap.description &&
    //   renderTaskMap.original &&
    //   renderTaskMap.video &&
    //   renderTaskMap.title
    // ) {
    //   if (!config.locationKeyword) {
    //     renderTaskMap.local = true
    //   } else {
    //     const localDom = getElementByText('span', '地点')

    //     console.log('localDom', localDom.parentElement)
    //     if (localDom && localDom.parentElement instanceof HTMLDivElement) {
    //       localDom.parentElement.click()

    //       await wait(1000)
    //       const localInputDom = document.querySelector(
    //         'input[placeholder="搜索添加相关地点，获得更多曝光"]',
    //       )
    //       if (localInputDom && localInputDom instanceof HTMLInputElement) {
    //         await onSendInput(localInputDom, config.locationKeyword, config.tabId)
    //         await wait(500)

    //         const selectDom = document.querySelector('.vue-recycle-scroller__item-view')

    //         if (selectDom && selectDom instanceof HTMLDivElement) {
    //           selectDom.click()
    //         }

    //         renderTaskMap.local = true
    //       }
    //     }
    //   }
    // }

    const taskList = Object.keys(renderTaskMap).filter((key) => !renderTaskMap[key])

    if (!taskList.length) {
      isDone = true
    }
  }
}
