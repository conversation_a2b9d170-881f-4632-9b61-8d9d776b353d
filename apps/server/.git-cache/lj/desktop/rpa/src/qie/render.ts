import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  categories: 0,
  tags: 0,
  description: 0
  // title: false,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.omui-button.omui-button--add + input')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        renderTaskMap.video = retryCount
        await wait(3000)
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const div = document.getElementById('-category_id')
      if (div && div instanceof HTMLDivElement) {
        const input = div.querySelector('.omui-suggestion__value')

        if (input && input instanceof HTMLInputElement) {
          void onSendInput(input, config.categories[1].text, config.tabId)
          let isDone = 0
          while (isDone < 3) {
            await wait(1000)
            const wapperDom = document.querySelector('.omui-suggestion__dropdown-wrap')
            if (wapperDom && wapperDom instanceof HTMLDivElement) {
              const groups = wapperDom.querySelectorAll('.group-option')

              try {
                groups.forEach((group) => {
                  console.log(group)
                  if (
                    group &&
                    group instanceof HTMLDivElement &&
                    group.textContent === config.categories[1].text
                  ) {
                    group.click()
                    renderTaskMap.categories = retryCount
                    throw new Error('')
                  }
                })
              } catch {
                break
              }
            }
            isDone++
          }
        }
      }
    } else {
      renderTaskMap.categories = retryCount
    }

    renderTaskMap.categories++
  }

  if (renderTaskMap.tags < retryCount) {
    if (config.tags.length) {
      const div = document.getElementById('-tag')

      if (div && div instanceof HTMLDivElement) {
        const input = div.querySelector('.omui-suggestion__value')

        if (input && input instanceof HTMLInputElement) {
          for (let i = 0; i < config.tags.length; i++) {
            void onSendInput(input, config.tags[i], config.tabId)
            await wait(300)
            void onSendEntryInput(input, config.tabId)
            await wait(300)
          }

          renderTaskMap.tags = retryCount
        }
      }
    } else {
      renderTaskMap.tags = retryCount
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.description < retryCount) {
    if (config.description) {
      const div = document.getElementById('-desc')

      if (div && div instanceof HTMLDivElement) {
        const input = div.querySelector('.omui-textarea__inner')

        if (input && input instanceof HTMLTextAreaElement) {
          manualDispatchFileEvent({
            dom: input,
            element: HTMLTextAreaElement,
            elementKey: 'value',
            value: config.description,
            event: 'change'
          })

          renderTaskMap.description = retryCount
        }
      }
    } else {
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => !renderTaskMap[key])
  if (!taskList.length) {
    isDone = true
  }
}
