import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  getElementByText,
  wait
} from '../renderUtils'

const startPush = () => {
  const div = document.querySelector('.video-batch-footer')

  if (div instanceof HTMLDivElement) {
    const childDiv = div.querySelector('.button-group')

    if (childDiv instanceof HTMLDivElement && childDiv.childNodes[2] instanceof HTMLButtonElement) {
      childDiv.childNodes[2].click()
    }
  }
  return false
}

const renderTaskMap = {
  video: 0,
  description: 0,
  tags: 0
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const videoDom = document.querySelector(
      '.byte-upload.xigua-upload-video-trigger.upload-video-trigger-card.no-tip'
    )

    if (videoDom && videoDom instanceof HTMLDivElement) {
      const inputDom = videoDom.querySelector('input[type="file"]')

      if (inputDom && inputDom instanceof HTMLInputElement) {
        const uint8Array = await getFileBuffer(config.videoPath)

        if (uint8Array) {
          const ext = getFileExtension(config.videoName)
          const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)
          const files = new DataTransfer()
          files.items.add(file)

          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: 'files',
            value: files.files,
            event: 'change'
          })

          renderTaskMap.video = retryCount
        }
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.tags < retryCount) {
    if (!config.tags.length) {
      renderTaskMap.tags = retryCount
    } else {
      const inputDom = document.querySelector('.arco-input-tag-input')

      if (inputDom && inputDom instanceof HTMLInputElement) {
        for (let i = 0; i < config.tags.length; i++) {
          await onSendInput(inputDom, `${config.tags[i]}`, config.tabId)
          await wait(1000)
          inputDom.blur()
          await wait(1000)
        }

        renderTaskMap.tags = retryCount
      }
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.description < retryCount) {
    if (!config.description) {
      renderTaskMap.description = retryCount
    } else {
      const descDom = document.querySelector('textarea[placeholder="请输入视频简介"]')

      if (descDom && descDom instanceof HTMLTextAreaElement) {
        await onSendInput(descDom, config.description, config.tabId)
        renderTaskMap.description = retryCount
      }
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
