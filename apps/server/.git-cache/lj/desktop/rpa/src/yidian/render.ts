import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  tags: 0,
  description: 0
  // categories: 0,
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.upload-before .upload-input')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)

        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        renderTaskMap.video = retryCount
        await wait(3000)
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.tags < retryCount) {
    if (config.tags.length) {
      const input = document.querySelector('.input.tag-input')

      if (input && input instanceof HTMLInputElement) {
        for (const item of config.tags) {
          await onSendInput(input, item, config.tabId)
          await wait(500)
          await onSendEntryInput(input, config.tabId)
          await wait(500)
        }
        renderTaskMap.tags = retryCount
      }
    } else {
      renderTaskMap.tags = retryCount
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.description < retryCount) {
    if (config.description) {
      const wapperDom = document.querySelector('.textarea-container')
      if (wapperDom && wapperDom instanceof HTMLDivElement) {
        const textareaDom = wapperDom.querySelector('textarea')

        if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
          manualDispatchFileEvent({
            dom: textareaDom,
            element: HTMLTextAreaElement,
            elementKey: 'value',
            value: config.description,
            event: 'change'
          })

          renderTaskMap.description = retryCount
        }
      }
    } else {
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }
  // TODO : 未完成
  // const pbDom = document.querySelector('.active.normal')

  // if (
  //   renderTaskMap.categories < retryCount &&
  //   pbDom.computedStyleMap().get('width').toString() === '100%'
  // ) {
  //   await wait(1000)
  //   if (config.categories.length) {
  //     const wapperDom = document.querySelector('.cate.row')
  //     if (wapperDom && wapperDom instanceof HTMLDivElement) {
  //       const inputDom = wapperDom.querySelector('.cascader-input')

  //       if (inputDom && inputDom instanceof HTMLInputElement) {
  //         manualDispatchFileEvent({
  //           dom: inputDom,
  //           element: HTMLInputElement,
  //           elementKey: 'value',
  //           value: `${config.categories[0].text}/${config.categories[1].text}`,
  //           event: 'change',
  //         })
  //         renderTaskMap.categories = retryCount
  //       }
  //     }
  //   } else {
  //     renderTaskMap.categories = retryCount
  //   }

  //   renderTaskMap.categories++
  // }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
