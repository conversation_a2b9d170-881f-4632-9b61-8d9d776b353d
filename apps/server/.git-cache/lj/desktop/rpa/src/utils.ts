import { genBaseCode } from './baseCode'
import type { IImageTask, IVideoTask } from './type'

export function wait(time = 1000) {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

export function videoRun(code: string, key: string) {
  return async (task: IVideoTask) => {
    const view = getView(task.tabId)

    try {
      await view.webContents.executeJavaScript(genBaseCode(code, task.tabId))
    } catch (error) {
      console.log('executeJavaScript', error)
    }

    while (true) {
      await wait(1000)
      if (!view.webContents) {
        break
      }

      if (view.webContents.isLoading()) {
        continue
      }

      await view.webContents.executeJavaScript(`
          isFocused = ${JSON.stringify(view.webContents.isFocused())}
        `)

      await view.webContents.executeJavaScript(`
          if (document) {
            renderVideo(${JSON.stringify(task)})
          }
        `)

      if (isActiveTab(task.tabId)) {
        await view.webContents.executeJavaScript(`
              if (document) {
                render(${JSON.stringify(task)})
              }
            `)

        const isDone = await view.webContents.executeJavaScript(`isDone`)
        if (isDone) {
          await view.webContents.executeJavaScript(`
              div.style.transform = 'translateX(-50%) scale(0)'
              div.style.opacity = '0'
            `)
          console.log(`render ${key} done`)
          break
        }
      }

      await wait(1000)
    }
  }
}

export function imageRun(code: string, key: string) {
  return async (task: IImageTask) => {
    const view = getView(task.tabId)

    try {
      await view.webContents.executeJavaScript(genBaseCode(code, task.tabId))
    } catch {
      //
    }

    while (true) {
      await wait(1000)
      if (!view.webContents) {
        break
      }

      if (view.webContents.isLoading()) {
        continue
      }

      await view.webContents.executeJavaScript(`
          isFocused = ${JSON.stringify(view.webContents.isFocused())}
        `)

      await view.webContents.executeJavaScript(`
          if (document) {
            renderImage(${JSON.stringify(task)})
          }
        `)

      if (isActiveTab(task.tabId)) {
        await view.webContents.executeJavaScript(`
              if (document) {
                render(${JSON.stringify(task)})
              }
            `)

        const isDone = await view.webContents.executeJavaScript(`isDone`)
        if (isDone) {
          await view.webContents.executeJavaScript(`
              div.style.transform = 'translateX(-50%) scale(0)'
              div.style.opacity = '0'
            `)
          console.log(`render ${key} done`)
          break
        }
      }

      await wait(1000)
    }
  }
}
