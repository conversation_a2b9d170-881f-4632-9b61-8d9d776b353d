import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  video: 0,
  description: 0,
  local: 0,
  title: 0
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const input = document.querySelector('.upload-input')

    if (input && input instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const file = uint8ArrayToFile(uint8Array, config.videoName, config.videoMime)
        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: input,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        renderTaskMap.video = retryCount
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.title < retryCount) {
    if (!config.title) {
      renderTaskMap.title = retryCount
    } else {
      const titleDom = document.querySelector('.input.titleInput')

      if (titleDom) {
        const inputdom = titleDom.querySelector('input')
        if (inputdom && inputdom instanceof HTMLInputElement) {
          await onSendInput(inputdom, config.title, config.tabId)

          renderTaskMap.title = retryCount
        }
      }
    }

    renderTaskMap.title++
  }

  if (renderTaskMap.description < retryCount) {
    const descDom = document.querySelector('.ql-editor.ql-blank')

    if (descDom && descDom instanceof HTMLDivElement) {
      await onSendInput(descDom, config.description, config.tabId)
      for (let i = 0; i < config.topic.length; i++) {
        const item = config.topic[i]

        if (item) {
          await wait(300)
          await onSendInput(descDom, `#${item}`, config.tabId)
          await wait(1000)
          await onSendEntryInput(descDom, config.tabId)
          await wait(300)
        }
      }

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  if (renderTaskMap.local < retryCount) {
    if (!config.locationKeyword) {
      renderTaskMap.local = retryCount
    } else {
      const divDom = document.querySelector('.d-select-input-filter.hide')
      if (divDom) {
        const inputDom = divDom.querySelector('input')
        if (inputDom && inputDom instanceof HTMLInputElement) {
          await onSendInput(inputDom, config.locationKeyword, config.tabId)
          await wait(3000)

          const item = document.querySelector('.d-grid-item')

          if (item) {
            ;(item as HTMLElement).click()
          }

          renderTaskMap.local = retryCount
        }
      }
    }

    renderTaskMap.local++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
