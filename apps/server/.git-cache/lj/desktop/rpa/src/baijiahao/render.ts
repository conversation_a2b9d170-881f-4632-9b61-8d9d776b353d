import type { IVideoTask } from '../type'
import {
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  uint8ArrayToFile,
  wait,
  retryCount,
  getFileExtension,
  onSendEntryInput,
  baseStartPush
} from '../renderUtils'

const renderTaskMap = {
  video: 0,
  tags: 0,
  description: 0
}

const startPush = () => {
  const buttonWapper = document.querySelector('.op-btn-outter-content')
  if (buttonWapper && buttonWapper instanceof HTMLElement) {
    const button = buttonWapper.querySelector('.cheetah-btn-primary')
    if (button && button instanceof HTMLButtonElement) {
      button.click()
      return true
    }
  }
  return baseStartPush()
}

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const videoDom = document.body.querySelector(
      '.video-main-container input[accept=".mp4, .mov, .mkv, .avi, .flv, .mpeg, .ogg, .vob, .webm, .wmv, .rmvb"]'
    )

    if (videoDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)
        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: videoDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        renderTaskMap.video = retryCount
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.tags < retryCount) {
    if (!config.tags) {
      renderTaskMap.tags = retryCount
    } else {
      const descDom = document.querySelector('.cheetah-ui-pro-tag-input-container-tag-input')

      if (descDom && descDom instanceof HTMLInputElement) {
        for (let i = 0; i < config.tags.length; i++) {
          try {
            await onSendInput(descDom, `${config.tags[i]} `, config.tabId)
            await wait(300)
            await onSendEntryInput(descDom, config.tabId)
          } catch {
            //
          }
        }

        renderTaskMap.tags = retryCount
      }
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.description < retryCount) {
    if (!config.description) {
      renderTaskMap.description = retryCount
    } else {
      const descDom = document.getElementById('desc')

      if (descDom && descDom instanceof HTMLTextAreaElement) {
        manualDispatchFileEvent({
          dom: descDom,
          element: HTMLTextAreaElement,
          elementKey: 'value',
          value: config.description,
          event: 'change'
        })

        renderTaskMap.description = retryCount
      }
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
