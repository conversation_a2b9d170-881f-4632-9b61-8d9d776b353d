declare global {
  const type: 'image-text' | 'video'
  const openUrl: (config: Placeholder, url: string) => Promise<string>
  const existsSync: (path: string) => boolean
  const getView: (tabId: string) => import('electron').WebContentsView
  const isActiveTab: (tabId: string) => boolean
  const startConfigs: import('./type').VideoTask[] | import('./type').ImageTask[]
  const platformNames: typeof import('@yixiaoer/platform-service/dist/injection/type').platformNames
  type Placeholder = any

  interface Window {
    app: {
      ipcRenderer: import('../../packages/preload/src/api').Api
    }
  }

  const __DOUYIN_RENDER_CODE__: string
  const __WEIBO_RENDER_CODE__: string
  const __KUAISHOU_RENDER_CODE__: string
  const __XIAOHONGSHU_RENDER_CODE__: string
  const __BAIJIAHAO_RENDER_CODE__: string
  const __BILIBILI_RENDER_CODE__: string
  const __SHIPINGHAO_RENDER_CODE__: string
  const __TOUTIAO_RENDER_CODE__: string
  const __ZHIHU_RENDER_CODE__: string
  const __QIE_RENDER_CODE__: string
  const __SOUHU_RENDER_CODE__: string
  const __YIDIAN_RENDER_CODE__: string
  const __WANGYI_RENDER_CODE__: string
  const __AQY_RENDER_CODE__: string
  const __WEISHI_RENDER_CODE__: string

  const __DOUYIN_IMAGE_RENDER_CODE__: string
  const __KUAISHOU_IMAGE_RENDER_CODE__: string
  const __XIAOHONGSHU_IMAGE_RENDER_CODE__: string
  const __SHIPINGHAO_IMAGE_RENDER_CODE__: string
  const __WEIBO_IMAGE_RENDER_CODE__: string

  let isDone: boolean
  let isFocused: boolean
}

declare module 'electron' {
  interface WebContentsView {
    isLoad?: boolean
  }
}

export {}
