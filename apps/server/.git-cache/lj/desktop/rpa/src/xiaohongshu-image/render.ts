import type { IImageTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  onSendEntryInput,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  image: 0,
  description: 0,
  title: 0,
}

async function renderImage(config: IImageTask) {
  const tabDom = document.querySelector('.upload-container')

  if (!tabDom) {
    return
  }

  const headerDOm = tabDom.querySelector('.header')

  if (headerDOm && headerDOm instanceof HTMLDivElement) {
    if (headerDOm.children[1] && headerDOm.children[1] instanceof HTMLDivElement) {
      headerDOm.children[1].click()
      await wait(200)
    }
  } else {
    return
  }

  if (renderTaskMap.image < retryCount) {
    const inputDom = tabDom.querySelector('.upload-input')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const files = new DataTransfer()

      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path)
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`,
          )

          files.items.add(file)
        }
      }

      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: 'files',
        value: files.files,
        event: 'change',
      })

      renderTaskMap.image = retryCount
      await wait(3000)
    }

    renderTaskMap.image++
  }
}

async function render(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    return
  }

  if (renderTaskMap.title < retryCount) {
    const inputDom = document.querySelector('.input.titleInput input')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: 'value',
        value: config.title,
        event: 'change',
      })

      renderTaskMap.title = retryCount
    }

    renderTaskMap.title++
  }

  if (renderTaskMap.description < retryCount) {
    const divDom = document.querySelector('#quillEditor .ql-editor')

    if (divDom && divDom instanceof HTMLDivElement) {
      const text = config.description.split('___!!!!___')

      for (let i = 0; i < text.length; i++) {
        const item = text[i]

        if (item) {
          if (i) {
            document.getElementById('topicBtn').click()
            await wait(1000)
            await onSendInput(divDom, item, config.tabId)
            await wait(1000)
            await onSendEntryInput(divDom, config.tabId)
            await wait(1000)
          } else {
            console.log(item)
            await onSendInput(divDom, item, config.tabId)
            await wait(1000)
          }
        }
      }

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}
