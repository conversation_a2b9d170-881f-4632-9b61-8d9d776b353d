import type { IVideoTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  getFileExtension,
  manualDispatchFileEvent,
  uint8ArrayToFile,
  baseStartPush,
  wait
} from '../renderUtils'

const startPush = () => {
  const button = document.querySelector('.VideoUploadForm-submitButton')
  if (button && button instanceof HTMLButtonElement) {
    button.click()
    return true
  }
  return baseStartPush()
}

const renderTaskMap = {
  video: 0,
  description: 0,
  original: 0,
  tags: 0,
  categories: 0
}

const retryCount = 5

async function renderVideo(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector('.VideoUploadButton-fileInput')

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath)

      if (uint8Array) {
        const ext = getFileExtension(config.videoName)
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime)
        const files = new DataTransfer()
        files.items.add(file)

        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: 'files',
          value: files.files,
          event: 'change'
        })

        await wait(2000)
        renderTaskMap.video = retryCount
      }
    }

    renderTaskMap.video++
  }
}

async function render(config: IVideoTask) {
  if (renderTaskMap.video < retryCount) {
    return
  }

  if (renderTaskMap.original < retryCount) {
    if (config.isOriginal) {
      const dom = getElementByText('label', '原创')

      if (dom && dom instanceof HTMLLabelElement) {
        dom.click()
        renderTaskMap.original = retryCount
      }
    } else {
      const dom = getElementByText('label', '转载')

      if (dom && dom instanceof HTMLLabelElement) {
        dom.click()
        renderTaskMap.original = retryCount
      }
    }

    renderTaskMap.original++
  }

  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const [a, b] = config.categories
      let isADone = 0

      const buttonA = document.getElementById('Popover8-toggle')
      if (buttonA instanceof HTMLButtonElement) {
        buttonA.click()

        await wait(300)
        const div = document.getElementById('Popover8-content')
        if (div instanceof HTMLDivElement) {
          const button = getElementByText('button', a.text, div)
          if (button instanceof HTMLButtonElement) {
            button.click()
            isADone++
          }
        }
      }

      const buttonB = document.getElementById('Popover10-toggle')
      if (buttonB instanceof HTMLButtonElement) {
        buttonB.click()

        await wait(300)
        const div = document.getElementById('Popover10-content')
        if (div instanceof HTMLDivElement) {
          const button = getElementByText('button', b.text, div)
          if (button instanceof HTMLButtonElement) {
            button.click()
            isADone++
          }
        }
      }

      if (isADone === 2) {
        renderTaskMap.categories = retryCount
      }
    } else {
      renderTaskMap.categories = retryCount
    }

    renderTaskMap.categories++
  }

  if (renderTaskMap.tags < retryCount) {
    const wapperDom = document.querySelector(
      '.TagInputAlias.TopicInputAlias-tagInput.VideoUploadForm-topicInput'
    )
    if (wapperDom && wapperDom instanceof HTMLDivElement) {
      const button = wapperDom.querySelector('button')
      if (button && button instanceof HTMLButtonElement) {
        button.click()
        await wait(200)
        const input = wapperDom.querySelector('input')
        if (input && input instanceof HTMLInputElement) {
          for (let i = 0; i < config.tags.length; i++) {
            manualDispatchFileEvent({
              dom: input,
              element: HTMLInputElement,
              elementKey: 'value',
              value: config.tags[i],
              event: 'change'
            })

            let j = 0
            await wait(2000)
            while (j < 10) {
              const list = document.querySelector('.TopicInputAlias-suggestionContainer')
              if (
                list &&
                list instanceof HTMLDivElement &&
                list.firstChild instanceof HTMLDivElement
              ) {
                list.firstChild.click()
              }
              j++
              await wait(300)
            }
          }

          renderTaskMap.tags = retryCount
        }
      }
    }

    renderTaskMap.tags++
  }

  if (renderTaskMap.description < retryCount) {
    const textareaDom = document.querySelector('textarea[rows="4"]')

    if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
      manualDispatchFileEvent({
        dom: textareaDom,
        element: HTMLTextAreaElement,
        elementKey: 'value',
        value: config.description,
        event: 'change'
      })
      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)

  if (!taskList.length) {
    isDone = true
  }
}
