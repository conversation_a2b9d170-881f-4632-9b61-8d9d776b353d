import { build } from 'vite'
import fs from 'fs-extra'
import { fileURLToPath } from 'url'
import path from 'path'

const filename = fileURLToPath(import.meta.url)
const dir = path.dirname(filename)

const entryPath = path.join(dir, 'src')

const files = fs.readdirSync(entryPath)

for (const item of files) {
  const currentPath = path.join(entryPath, item)

  if (fs.statSync(currentPath).isDirectory()) {
    const renderFile = path.join(currentPath, 'render.ts')

    if (fs.existsSync(renderFile)) {
      await build({
        root: dir,
        configFile: false,
        esbuild: {
          keepNames: true,
          treeShaking: false,
        },
        build: {
          minify: false,

          emptyOutDir: false,
          outDir: path.join(dir, 'dist-render'),

          lib: {
            entry: renderFile,
            formats: ['es'],
          },
          rollupOptions: {
            treeshake: false,
            output: {
              entryFileNames: `${item}.js`,
            },
          },
        },
      })
    }
  }
}
