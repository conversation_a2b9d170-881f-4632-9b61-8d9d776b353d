{"name": "@yixiaoer-lite/rpa", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build-node": "vite build --config vite.config.ts", "build-render": "node build-render.mjs", "build": "pnpm run build-render && pnpm run build-node", "build:prod": "cross-env MIN=true pnpm run build-render && cross-env MIN=true pnpm run build-node", "build-watch": "node build-watch.mjs"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"terser": "5.39.0"}}