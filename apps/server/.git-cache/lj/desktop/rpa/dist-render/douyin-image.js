var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
const retryCount = 5;
function manualDispatchFileEvent({
  dom,
  element,
  elementKey,
  event,
  value
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey);
  if (proto && proto.set && dom) {
    proto.set.call(dom, value);
    dom.dispatchEvent(new Event(event, { bubbles: true }));
  }
}
__name(manualDispatchFileEvent, "manualDispatchFileEvent");
function uint8ArrayToFile(uint8Array, fileName, mimeType) {
  const blob = new Blob([uint8Array], { type: mimeType });
  const file = new File([blob], fileName, { type: mimeType });
  return file;
}
__name(uint8ArrayToFile, "uint8ArrayToFile");
function wait(time = 1e3) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}
__name(wait, "wait");
function getFileBuffer(filePath) {
  return window.app.ipcRenderer.invoke("read-file-buffer", filePath);
}
__name(getFileBuffer, "getFileBuffer");
function onSendInput(dom, value, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-event", value, tabId);
}
__name(onSendInput, "onSendInput");
function onSendEntryInput(dom, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-entry-event", tabId);
}
__name(onSendEntryInput, "onSendEntryInput");
function baseStartPush() {
  const start = getElementByText("button", "发布");
  if (start && start instanceof HTMLButtonElement) {
    start.click();
    return true;
  }
  const start5 = getElementByText("button", "发表");
  if (start5 && start5 instanceof HTMLButtonElement) {
    start5.click();
    return true;
  }
  const start1 = getElementByText("div", "发布");
  if (start1 && start1 instanceof HTMLDivElement) {
    start1.click();
    return true;
  }
  const start2 = getElementByText("span", "发 布");
  if (start2 && start2 instanceof HTMLSpanElement) {
    start2.click();
    return true;
  }
  const start3 = getElementByText("span", "发布");
  if (start3 && start3 instanceof HTMLSpanElement) {
    start3.click();
    return true;
  }
  const start4 = getElementByText("span", "立即投稿");
  if (start4 && start4 instanceof HTMLSpanElement) {
    start4.click();
    return true;
  }
  return false;
}
__name(baseStartPush, "baseStartPush");
function getElementByText(dom, text, container = document) {
  const xpathResult = document.evaluate(
    `//${dom}[text()='${text}']`,
    container,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  try {
    return xpathResult.singleNodeValue;
  } catch {
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
      const node = xpathResult.snapshotItem(i);
      if (node) {
        return node;
      }
    }
  }
}
__name(getElementByText, "getElementByText");
function getFileExtension(filename) {
  return filename.substring(filename.lastIndexOf(".") + 1);
}
__name(getFileExtension, "getFileExtension");
async function retry(fn, maxRetries = 5) {
  let attempts = 0;
  while (attempts < maxRetries) {
    try {
      const result = await fn();
      if (result) {
        return;
      }
    } catch {
    }
    attempts++;
    await wait(1e3);
  }
}
__name(retry, "retry");
const startPush = baseStartPush;
const renderTaskMap = {
  image: 0,
  title: 0,
  description: 0,
  music: 0
};
async function renderImage(config) {
  if (renderTaskMap.image < retryCount) {
    const inputDom = document.querySelector(
      '.semi-tabs-pane-motion-overlay input[accept="image/png,image/jpeg,image/jpg,image/bmp,image/webp,image/tif"][multiple]'
    );
    if (inputDom && inputDom instanceof HTMLInputElement) {
      const files = new DataTransfer();
      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path);
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`
          );
          files.items.add(file);
        }
      }
      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: "files",
        value: files.files,
        event: "change"
      });
      renderTaskMap.image = retryCount;
      await wait(3e3);
    }
    renderTaskMap.image++;
  }
}
__name(renderImage, "renderImage");
async function render(config) {
  if (renderTaskMap.image < retryCount) {
    return;
  }
  if (renderTaskMap.title < retryCount) {
    const inputDom = document.querySelector('.semi-input-wrapper input[placeholder="添加作品标题"]');
    if (inputDom && inputDom instanceof HTMLInputElement) {
      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: "value",
        value: config.title,
        event: "change"
      });
      renderTaskMap.title = retryCount;
    }
    renderTaskMap.title++;
  }
  if (renderTaskMap.description < retryCount) {
    const divDom = document.querySelector(".editor.editor-comp-publish");
    console.log(config.description);
    if (divDom && divDom instanceof HTMLDivElement) {
      await onSendInput(divDom, config.description, config.tabId);
      renderTaskMap.description = retryCount;
    }
    renderTaskMap.description++;
  }
  if (renderTaskMap.music < retryCount) {
    if (config.music) {
      const spanDom = getElementByText("span", "选择音乐");
      if (spanDom && spanDom instanceof HTMLSpanElement) {
        spanDom.click();
        await wait(300);
        const inputDom = document.querySelector('input[placeholder="搜索音乐"]');
        if (inputDom && inputDom instanceof HTMLInputElement) {
          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: "value",
            value: config.music,
            event: "change"
          });
          await wait(1e3);
          let count = 0;
          while (count < 10) {
            const listDom = document.querySelector('[class^="audio-collection-container-"]');
            if (listDom && listDom instanceof HTMLDivElement) {
              const items = listDom.querySelectorAll('[class^="card-container-"]');
              if (items && items.length) {
                for (const item of items) {
                  const titleDom = item.querySelector('[class^="audio-name-"]');
                  if (titleDom && titleDom instanceof HTMLDivElement) {
                    if (titleDom.textContent === config.music) {
                      const spanDom2 = getElementByText("span", "使用", item);
                      if (spanDom2 && spanDom2 instanceof HTMLSpanElement && spanDom2.parentElement instanceof HTMLButtonElement) {
                        spanDom2.parentElement.click();
                        break;
                      }
                    }
                  }
                }
              }
            }
            count++;
            await wait(1e3);
          }
          renderTaskMap.music = retryCount;
        }
      }
    } else {
      renderTaskMap.music = retryCount;
    }
    renderTaskMap.music++;
  }
  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount);
  if (!taskList.length) {
    isDone = true;
  }
}
__name(render, "render");
