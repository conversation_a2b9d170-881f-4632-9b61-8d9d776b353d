var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
const retryCount = 5;
function manualDispatchFileEvent({
  dom,
  element,
  elementKey,
  event,
  value
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey);
  if (proto && proto.set && dom) {
    proto.set.call(dom, value);
    dom.dispatchEvent(new Event(event, { bubbles: true }));
  }
}
__name(manualDispatchFileEvent, "manualDispatchFileEvent");
function uint8ArrayToFile(uint8Array, fileName, mimeType) {
  const blob = new Blob([uint8Array], { type: mimeType });
  const file = new File([blob], fileName, { type: mimeType });
  return file;
}
__name(uint8ArrayToFile, "uint8ArrayToFile");
function wait(time = 1e3) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}
__name(wait, "wait");
function getFileBuffer(filePath) {
  return window.app.ipcRenderer.invoke("read-file-buffer", filePath);
}
__name(getFileBuffer, "getFileBuffer");
function onSendInput(dom, value, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-event", value, tabId);
}
__name(onSendInput, "onSendInput");
function onSendEntryInput(dom, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-entry-event", tabId);
}
__name(onSendEntryInput, "onSendEntryInput");
function baseStartPush() {
  const start = getElementByText("button", "发布");
  if (start && start instanceof HTMLButtonElement) {
    start.click();
    return true;
  }
  const start5 = getElementByText("button", "发表");
  if (start5 && start5 instanceof HTMLButtonElement) {
    start5.click();
    return true;
  }
  const start1 = getElementByText("div", "发布");
  if (start1 && start1 instanceof HTMLDivElement) {
    start1.click();
    return true;
  }
  const start2 = getElementByText("span", "发 布");
  if (start2 && start2 instanceof HTMLSpanElement) {
    start2.click();
    return true;
  }
  const start3 = getElementByText("span", "发布");
  if (start3 && start3 instanceof HTMLSpanElement) {
    start3.click();
    return true;
  }
  const start4 = getElementByText("span", "立即投稿");
  if (start4 && start4 instanceof HTMLSpanElement) {
    start4.click();
    return true;
  }
  return false;
}
__name(baseStartPush, "baseStartPush");
function getElementByText(dom, text, container = document) {
  const xpathResult = document.evaluate(
    `//${dom}[text()='${text}']`,
    container,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  try {
    return xpathResult.singleNodeValue;
  } catch {
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
      const node = xpathResult.snapshotItem(i);
      if (node) {
        return node;
      }
    }
  }
}
__name(getElementByText, "getElementByText");
function getFileExtension(filename) {
  return filename.substring(filename.lastIndexOf(".") + 1);
}
__name(getFileExtension, "getFileExtension");
async function retry(fn, maxRetries = 5) {
  let attempts = 0;
  while (attempts < maxRetries) {
    try {
      const result = await fn();
      if (result) {
        return;
      }
    } catch {
    }
    attempts++;
    await wait(1e3);
  }
}
__name(retry, "retry");
const startPush = baseStartPush;
const renderTaskMap = {
  image: 0,
  description: 0,
  music: 0
};
async function renderImage(config) {
  if (renderTaskMap.image < retryCount) {
    const wapperDom = document.querySelector('#rc-tabs-0-panel-2 div[class^="_dragger-content_"]');
    if (wapperDom && wapperDom instanceof HTMLDivElement) {
      const files = new DataTransfer();
      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path);
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`
          );
          files.items.add(file);
        }
      }
      const dropEvent = new DragEvent("drop", {
        bubbles: true,
        cancelable: true,
        dataTransfer: files
      });
      wapperDom.dispatchEvent(dropEvent);
      renderTaskMap.image = retryCount;
      await wait(3e3);
    }
    renderTaskMap.image++;
  }
}
__name(renderImage, "renderImage");
async function render(config) {
  if (renderTaskMap.image < retryCount) {
    return;
  }
  if (renderTaskMap.description < retryCount) {
    const divDom = document.getElementById("work-description-edit");
    if (divDom && divDom instanceof HTMLDivElement) {
      const text = config.description.split("___!!!!___");
      for (let i = 0; i < text.length; i++) {
        const item = text[i];
        if (item) {
          if (i) {
            await onSendInput(divDom, "#", config.tabId);
            await wait(300);
            await onSendInput(divDom, `${item} `, config.tabId);
            await wait(300);
          } else {
            await onSendInput(divDom, item, config.tabId);
            await wait(300);
          }
        }
      }
      renderTaskMap.description = retryCount;
    }
    renderTaskMap.description++;
  }
  if (renderTaskMap.music < retryCount) {
    if (config.music) {
      const spanDom = getElementByText("div", "添加音乐");
      if (spanDom && spanDom instanceof HTMLDivElement) {
        spanDom.click();
        await wait(300);
        const wapperDom = document.getElementById("microSupport");
        const inputDom = wapperDom.querySelector('input[placeholder="搜索音乐"]');
        if (inputDom && inputDom instanceof HTMLInputElement) {
          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: "value",
            value: config.music,
            event: "change"
          });
          await wait(1e3);
          let count = 0;
          while (count < 10) {
            const items = wapperDom.querySelectorAll('[class^="_item_"]');
            if (items && items.length) {
              for (const item of items) {
                const titleDom = item.querySelector('[class^="_info-title_"]');
                if (titleDom && titleDom instanceof HTMLDivElement) {
                  if (titleDom.textContent === config.music) {
                    const spanDom2 = getElementByText("div", "添加", item);
                    if (spanDom2 && spanDom2 instanceof HTMLDivElement) {
                      spanDom2.parentElement.click();
                      break;
                    }
                  }
                }
              }
            }
            count++;
            await wait(1e3);
          }
          renderTaskMap.music = retryCount;
        }
      }
    } else {
      renderTaskMap.music = retryCount;
    }
    renderTaskMap.music++;
  }
  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount);
  if (!taskList.length) {
    isDone = true;
  }
}
__name(render, "render");
