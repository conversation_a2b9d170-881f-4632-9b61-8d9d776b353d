var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
const retryCount$1 = 5;
function manualDispatchFileEvent({
  dom,
  element,
  elementKey,
  event,
  value
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey);
  if (proto && proto.set && dom) {
    proto.set.call(dom, value);
    dom.dispatchEvent(new Event(event, { bubbles: true }));
  }
}
__name(manualDispatchFileEvent, "manualDispatchFileEvent");
function uint8ArrayToFile(uint8Array, fileName, mimeType) {
  const blob = new Blob([uint8Array], { type: mimeType });
  const file = new File([blob], fileName, { type: mimeType });
  return file;
}
__name(uint8ArrayToFile, "uint8ArrayToFile");
function wait(time = 1e3) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}
__name(wait, "wait");
function getFileBuffer(filePath) {
  return window.app.ipcRenderer.invoke("read-file-buffer", filePath);
}
__name(getFileBuffer, "getFileBuffer");
function onSendInput(dom, value, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-event", value, tabId);
}
__name(onSendInput, "onSendInput");
function onSendEntryInput(dom, tabId) {
  dom.focus();
  return window.app.ipcRenderer.invoke("rpa-send-entry-event", tabId);
}
__name(onSendEntryInput, "onSendEntryInput");
function baseStartPush() {
  const start = getElementByText("button", "发布");
  if (start && start instanceof HTMLButtonElement) {
    start.click();
    return true;
  }
  const start5 = getElementByText("button", "发表");
  if (start5 && start5 instanceof HTMLButtonElement) {
    start5.click();
    return true;
  }
  const start1 = getElementByText("div", "发布");
  if (start1 && start1 instanceof HTMLDivElement) {
    start1.click();
    return true;
  }
  const start2 = getElementByText("span", "发 布");
  if (start2 && start2 instanceof HTMLSpanElement) {
    start2.click();
    return true;
  }
  const start3 = getElementByText("span", "发布");
  if (start3 && start3 instanceof HTMLSpanElement) {
    start3.click();
    return true;
  }
  const start4 = getElementByText("span", "立即投稿");
  if (start4 && start4 instanceof HTMLSpanElement) {
    start4.click();
    return true;
  }
  return false;
}
__name(baseStartPush, "baseStartPush");
function getElementByText(dom, text, container = document) {
  const xpathResult = document.evaluate(
    `//${dom}[text()='${text}']`,
    container,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  try {
    return xpathResult.singleNodeValue;
  } catch {
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
      const node = xpathResult.snapshotItem(i);
      if (node) {
        return node;
      }
    }
  }
}
__name(getElementByText, "getElementByText");
function getFileExtension(filename) {
  return filename.substring(filename.lastIndexOf(".") + 1);
}
__name(getFileExtension, "getFileExtension");
async function retry(fn, maxRetries = 5) {
  let attempts = 0;
  while (attempts < maxRetries) {
    try {
      const result = await fn();
      if (result) {
        return;
      }
    } catch {
    }
    attempts++;
    await wait(1e3);
  }
}
__name(retry, "retry");
const startPush = /* @__PURE__ */ __name(() => {
  const button = document.querySelector(".VideoUploadForm-submitButton");
  if (button && button instanceof HTMLButtonElement) {
    button.click();
    return true;
  }
  return baseStartPush();
}, "startPush");
const renderTaskMap = {
  video: 0,
  description: 0,
  original: 0,
  tags: 0,
  categories: 0
};
const retryCount = 5;
async function renderVideo(config) {
  if (renderTaskMap.video < retryCount) {
    const inputDom = document.querySelector(".VideoUploadButton-fileInput");
    if (inputDom && inputDom instanceof HTMLInputElement) {
      const uint8Array = await getFileBuffer(config.videoPath);
      if (uint8Array) {
        const ext = getFileExtension(config.videoName);
        const file = uint8ArrayToFile(uint8Array, `${config.title}.${ext}`, config.videoMime);
        const files = new DataTransfer();
        files.items.add(file);
        manualDispatchFileEvent({
          dom: inputDom,
          element: HTMLInputElement,
          elementKey: "files",
          value: files.files,
          event: "change"
        });
        await wait(2e3);
        renderTaskMap.video = retryCount;
      }
    }
    renderTaskMap.video++;
  }
}
__name(renderVideo, "renderVideo");
async function render(config) {
  if (renderTaskMap.video < retryCount) {
    return;
  }
  if (renderTaskMap.original < retryCount) {
    if (config.isOriginal) {
      const dom = getElementByText("label", "原创");
      if (dom && dom instanceof HTMLLabelElement) {
        dom.click();
        renderTaskMap.original = retryCount;
      }
    } else {
      const dom = getElementByText("label", "转载");
      if (dom && dom instanceof HTMLLabelElement) {
        dom.click();
        renderTaskMap.original = retryCount;
      }
    }
    renderTaskMap.original++;
  }
  if (renderTaskMap.categories < retryCount) {
    if (config.categories.length) {
      const [a, b] = config.categories;
      let isADone = 0;
      const buttonA = document.getElementById("Popover8-toggle");
      if (buttonA instanceof HTMLButtonElement) {
        buttonA.click();
        await wait(300);
        const div = document.getElementById("Popover8-content");
        if (div instanceof HTMLDivElement) {
          const button = getElementByText("button", a.text, div);
          if (button instanceof HTMLButtonElement) {
            button.click();
            isADone++;
          }
        }
      }
      const buttonB = document.getElementById("Popover10-toggle");
      if (buttonB instanceof HTMLButtonElement) {
        buttonB.click();
        await wait(300);
        const div = document.getElementById("Popover10-content");
        if (div instanceof HTMLDivElement) {
          const button = getElementByText("button", b.text, div);
          if (button instanceof HTMLButtonElement) {
            button.click();
            isADone++;
          }
        }
      }
      if (isADone === 2) {
        renderTaskMap.categories = retryCount;
      }
    } else {
      renderTaskMap.categories = retryCount;
    }
    renderTaskMap.categories++;
  }
  if (renderTaskMap.tags < retryCount) {
    const wapperDom = document.querySelector(
      ".TagInputAlias.TopicInputAlias-tagInput.VideoUploadForm-topicInput"
    );
    if (wapperDom && wapperDom instanceof HTMLDivElement) {
      const button = wapperDom.querySelector("button");
      if (button && button instanceof HTMLButtonElement) {
        button.click();
        await wait(200);
        const input = wapperDom.querySelector("input");
        if (input && input instanceof HTMLInputElement) {
          for (let i = 0; i < config.tags.length; i++) {
            manualDispatchFileEvent({
              dom: input,
              element: HTMLInputElement,
              elementKey: "value",
              value: config.tags[i],
              event: "change"
            });
            let j = 0;
            await wait(2e3);
            while (j < 10) {
              const list = document.querySelector(".TopicInputAlias-suggestionContainer");
              if (list && list instanceof HTMLDivElement && list.firstChild instanceof HTMLDivElement) {
                list.firstChild.click();
              }
              j++;
              await wait(300);
            }
          }
          renderTaskMap.tags = retryCount;
        }
      }
    }
    renderTaskMap.tags++;
  }
  if (renderTaskMap.description < retryCount) {
    const textareaDom = document.querySelector('textarea[rows="4"]');
    if (textareaDom && textareaDom instanceof HTMLTextAreaElement) {
      manualDispatchFileEvent({
        dom: textareaDom,
        element: HTMLTextAreaElement,
        elementKey: "value",
        value: config.description,
        event: "change"
      });
      renderTaskMap.description = retryCount;
    }
    renderTaskMap.description++;
  }
  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount);
  if (!taskList.length) {
    isDone = true;
  }
}
__name(render, "render");
