function manualDispatchFileEvent({
  dom,
  element,
  elementKey,
  event,
  value
}) {
  const proto = Object.getOwnPropertyDescriptor(element.prototype, elementKey);
  if (proto && proto.set && dom) {
    proto.set.call(dom, value);
    dom.dispatchEvent(new Event(event, { bubbles: true }));
  }
}
function uint8ArrayToFile(uint8Array, fileName, mimeType) {
  const blob = new Blob([uint8Array], { type: mimeType });
  const file = new File([blob], fileName, { type: mimeType });
  return file;
}
function wait(time = 1e3) {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
}
function getFileBuffer(filePath) {
  return window.api.invoke("readFileBuffer", filePath);
}
function onSendInput(dom, value, tabId) {
  dom.focus();
  return window.api.invoke("rpa-send-event", value, tabId);
}
function getElementByText(dom, text) {
  const xpathResult = document.evaluate(
    `//${dom}[text()='${text}']`,
    document,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  try {
    return xpathResult.singleNodeValue;
  } catch {
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
      const node = xpathResult.snapshotItem(i);
      if (node) {
        return node;
      }
    }
  }
}
const renderTaskMap = {
  video: false,
  title: false,
  description: false
};
async function render(config) {
  if (!document) {
    return;
  }
  if (!renderTaskMap.video) {
    const videoDom = document.querySelector(".bcc-upload-wrapper");
    if (videoDom && videoDom instanceof HTMLDivElement) {
      const inputDom = videoDom.querySelector('input[type="file"]');
      if (inputDom && inputDom instanceof HTMLInputElement) {
        const uint8Array = await getFileBuffer(config.videoPath);
        if (uint8Array) {
          const file = uint8ArrayToFile(uint8Array, config.videoFileName, config.videoMime);
          const files = new DataTransfer();
          files.items.add(file);
          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: "files",
            value: files.files,
            event: "change"
          });
          renderTaskMap.video = true;
        }
      }
    }
  }
  if (!renderTaskMap.title) {
    if (!config.title) {
      renderTaskMap.title = true;
    } else {
      const titleDom = document.querySelector('input[placeholder="【剪辑类型】+主要标题"]');
      if (titleDom && titleDom instanceof HTMLInputElement) {
        await onSendInput(titleDom, config.title, config.tabId);
        if (titleDom.value === config.title) {
          renderTaskMap.title = true;
        }
      }
    }
  }
  if (!renderTaskMap.description) {
    if (!config.description) {
      renderTaskMap.description = true;
    } else {
      const descDom = document.querySelector(".ql-editor.ql-blank");
      if (descDom && descDom instanceof HTMLDivElement) {
        await onSendInput(descDom, config.description, config.tabId);
        renderTaskMap.description = true;
      }
    }
  }
  const taskList = Object.keys(renderTaskMap).filter((key) => !renderTaskMap[key]);
  if (!taskList.length) {
    isDone = true;
  }
}
