import chokidar from 'chokidar'
import { fileURLToPath } from 'url'
import path from 'path'
import { execSync } from 'child_process'

const filename = fileURLToPath(import.meta.url)
const dir = path.dirname(filename)

const inputDir = path.join(dir, 'src')

execSync('pnpm run build')

chokidar
  .watch(inputDir, {
    ignoreInitial: true,
  })
  .on('all', async (event, path) => {
    execSync('pnpm run build')

    console.log('\x1B[36m%s\x1B[0m', 'event:', event, 'path:', path)
  })

console.log('start watch...')
