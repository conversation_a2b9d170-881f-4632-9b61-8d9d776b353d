stages:
  - MergeRequest
  - Development
  - Integrated
  - PreProduction
  - Production

variables:
  # 安全文件下载目标路径
  SECURE_FILES_DOWNLOAD_PATH: 'build'
  DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/download-secure-files-windows-386.exe'
  DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/download-secure-files-windows-amd64.exe'
  DOWNLOAD_SECURE_FILES_INSTALL_URL: 'https://registry.coozf.com/repository/raw/devops/gitlab-runner/installer'

before_script:
  - which node
  - node -v
#  - pnpm -v

# ---------------------------
# 合并请求
# ---------------------------
build:
  image: node:16
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  stage: MergeRequest
  script:
    - pnpm cache clean --force
    - pnpm install --force
    - pnpm run build

# ---------------------------
# 用于检查代码是否符合规范
# ---------------------------
#sonarqube-check:
#  image:
#    name: sonarsource/sonar-scanner-cli:4.8.1
#    entrypoint: ['']
#  rules:
#    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#  stage: MergeRequest
#  variables:
#    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
#    GIT_DEPTH: '0'
#  cache:
#    key: '${CI_JOB_NAME}'
#    paths:
#      - .sonar/cache
#  script:
#    - sonar-scanner

# ---------------------------
# Windows
# ---------------------------

# 测试环境 64位
#Integrated Win32 x64:
#  stage: Integrated
#  only:
#    - /^v\d+\.\d+\.\d+-alpha.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
#    - /^v\d+\.\d+\.\d+-beta.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
#  tags:
#    - win32-x64
#  before_script:
#    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL -OutFile "download-secure-files-windows.exe"
#    - Start-Process "download-secure-files-windows.exe"
#  script:
#    - pnpm i
#    - pnpm run build-app:staging:win-x64
#  artifacts:
#    paths:
#      - dist/*.exe
#      - dist/*.exe.blockmap
#      - dist/*.yml
#    exclude:
#      - dist/builder-debug.yml
#  environment:
#    name: Development

# 测试环境 32位
#Integrated Win32 ia32:
#  stage: Integrated
#  only:
#    - /^v\d+\.\d+\.\d+-alpha.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
#    - /^v\d+\.\d+\.\d+-beta.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
#  tags:
#    - win32-x64
#  before_script:
#    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL -OutFile "download-secure-files-windows.exe"
#    - Start-Process "download-secure-files-windows.exe"
#  script:
#    - pnpm install --config.platform=win32 --config.architecture=ia32 sharp
#    - Remove-Item -Path ".\node_modules\baidu-aip-sdk\node_modules\iconv-lite\" -Recurse -Force #删除导致质量检测失败的文件
#    - pnpm run electron:build:test -- --win --ia32
#  artifacts:
#    paths:
#      - dist/*.exe
#      - dist/*.exe.blockmap
#      - dist/*.yml
#    exclude:
#      - dist/builder-debug.yml
#  environment:
#    name: Development

# 预生产环境 64位
PreProduction Win32 x64:
  stage: PreProduction
  only:
    - /^v\d+\.\d+\.\d+-alpha-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
    - /^v\d+\.\d+\.\d+-beta-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
  tags:
    - win32-x64
  before_script:
    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL -OutFile "download-secure-files-windows.exe"
    - Start-Process "download-secure-files-windows.exe"
  script:
    - pnpm i --config.platform=win32 --config.architecture=x64
    - pnpm run build-app:staging:win-x64
    - node ./build/publish.js staging $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: PreProduction

# 预生产环境 32位
PreProduction Win32 ia32:
  stage: PreProduction
  only:
    - /^v\d+\.\d+\.\d+-alpha-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
    - /^v\d+\.\d+\.\d+-beta-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
  tags:
    - win32-x64
  before_script:
    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL -OutFile "download-secure-files-windows.exe"
    - Start-Process "download-secure-files-windows.exe"
  script:
    - pnpm i --config.platform=win32 --config.architecture=ia32
    - pnpm run build-app:staging:win-ia32
    - node ./build/publish.js staging $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: PreProduction

# 生产环境 64位
Production Win32 x64:
  stage: Production
  only:
    - /^v\d+\.\d+\.\d+-production-open$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0
    - /^v\d+\.\d+\.\d+-rc.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-rc.1
  tags:
    - win32-x64
  before_script:
    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_X64_URL -OutFile "download-secure-files-windows.exe"
    - Start-Process "download-secure-files-windows.exe"
  script:
    - pnpm i --config.platform=win32 --config.architecture=x64
    - pnpm run build-app:production:win-x64
    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: Production

# 生产环境 32位
Production Win32 ia32:
  stage: Production
  only:
    - /^v\d+\.\d+\.\d+-production-open$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0
    - /^v\d+\.\d+\.\d+-rc.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-rc.1
  tags:
    - win32-x64
  before_script:
    - Invoke-WebRequest -Uri $DOWNLOAD_SECURE_FILES_WINDOWS_IA32_URL -OutFile "download-secure-files-windows.exe"
    - Start-Process "download-secure-files-windows.exe"
  script:
    - pnpm i --config.platform=win32 --config.architecture=ia32
    - pnpm run build-app:production:win-ia32
    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: Production

# ---------------------------
# macOS
# ---------------------------

# 测试环境 x64
#Integrated macOS x64:
#  stage: Integrated
#  only:
#    - /^v\d+\.\d+\.\d+-alpha.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
#    - /^v\d+\.\d+\.\d+-beta.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
#  tags:
#    - darwin-x64-local
##    - wang
#  before_script:
#    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
#  script:
#    - pnpm i
#    - pnpm run build-app:staging:mac-x64
#    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
#  environment:
#    name: Development

# 测试环境 arm64
#Integrated macOS arm64:
#  stage: Integrated
#  only:
#    - /^v\d+\.\d+\.\d+-alpha.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
#    - /^v\d+\.\d+\.\d+-beta.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
#  tags:
##    - darwin-x64-local
#    - wang
#  before_script:
#    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
#  script:
#    - pnpm i
#    - pnpm run build-app:staging:mac-arm64
#    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
#  environment:
#    name: Development

# 预生产环境 x64
PreProduction macOS x64:
  stage: PreProduction
  only:
    - /^v\d+\.\d+\.\d+-alpha-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
    - /^v\d+\.\d+\.\d+-beta-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
  tags:
    - darwin-x64-local
  before_script:
    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
  script:
    - pnpm i --config.platform=darwin --config.architecture=x64
    - pnpm run build-app:staging:mac-x64
    - node ./build/publish.js staging $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: PreProduction

# 预生产环境 arm64
PreProduction macOS arm64:
  stage: PreProduction
  only:
    - /^v\d+\.\d+\.\d+-alpha-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-alpha.1
    - /^v\d+\.\d+\.\d+-beta-open.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-beta.1
  tags:
    - darwin-x64-local
    # - wang
  before_script:
    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
  script:
    - pnpm i --config.platform=darwin --config.architecture=arm64
    - pnpm run build-app:staging:mac-arm64
    - node ./build/publish.js staging $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: PreProduction

# 生产环境 x64
Production macOS x64:
  stage: Production
  only:
    - /^v\d+\.\d+\.\d+-production-open$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0
    - /^v\d+\.\d+\.\d+-rc.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-rc.1
  tags:
    - darwin-x64-local
  before_script:
    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
  script:
    - pnpm i --config.platform=darwin --config.architecture=x64
    - pnpm run build-app:production:mac-x64
    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: Production

# 生产环境 arm64
Production macOS arm64:
  stage: Production
  only:
    - /^v\d+\.\d+\.\d+-production-open$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0
    - /^v\d+\.\d+\.\d+-rc.\d+$/ # 以v开头，后面跟着数字，数字之间用.分隔，例如v1.0.0-rc.1
  tags:
    - darwin-x64-local
  #    - wang
  before_script:
    - curl -s $DOWNLOAD_SECURE_FILES_INSTALL_URL | bash
  script:
    - pnpm i --config.platform=darwin --config.architecture=arm64
    - pnpm run build-app:production:mac-arm64
    - node ./build/publish.js prod $CI_TOS_ACCESS_KEY_ID $CI_TOS_ACCESS_KEY_SECRET $CI_COMMIT_REF_NAME #推送到OSS
  environment:
    name: Production
