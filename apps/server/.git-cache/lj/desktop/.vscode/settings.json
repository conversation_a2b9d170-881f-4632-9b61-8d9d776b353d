{"typescript.tsdk": "node_modules/typescript/lib", "eslint.useFlatConfig": true, "eslint.options": {"overrideConfigFile": "./eslint.config.mjs"}, "[typescript]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "files.associations": {"*.md": "markdown", "*.cjson": "jsonc", "*.wxss": "css", "*.wxs": "javascript", "*.snap": "plaintext", "*.x": "objective-cpp", "*.css": "tailwindcss", "xiosbase": "cpp", "xstring": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "format": "cpp", "functional": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "memory": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "xutility": "cpp", "xtree": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "resumable": "cpp", "future": "cpp", "map": "cpp", "mutex": "cpp", "ratio": "cpp", "stop_token": "cpp", "thread": "cpp", "iostream": "cpp", "istream": "cpp"}}