import { FastifyInstance } from 'fastify'

export async function gitlabWebhookRoutes(app: FastifyInstance) {
  //   app.addHook('preHandler', async (request, _reply) => {
  //     const  = request.headers['X-Gitlab-Token']
  //     if (authHeader !== `Bearer ${process.env.GITLAB_WEBHOOK_SECRET}`) {
  //       throw new Error('Unauthorized')
  //     }
  //   })
  app.post('/', async (request, reply) => {
    console.log('收到GitLab Webhook请求', request.body)
    return reply.send({ success: true })
  })
}
