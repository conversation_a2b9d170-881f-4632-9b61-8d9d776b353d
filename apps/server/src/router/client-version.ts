import { z } from 'zod'
import { router } from '@/trpc'
import { ClientVersionService } from '@/lib/business/client-version'
import { ApplicationService } from '@/lib/business/application'
import { applicationProcedure } from '@/procedure'
import { CreateClientVersionSchema, ClientVersionListSchema, OEMConfigSchema, PlatformSchema } from '@coozf/zod'
import { TRPCError } from '@trpc/server'

export const userClientVersionRouter = router({
  // 用户创建自己应用的客户端版本
  create: applicationProcedure.input(CreateClientVersionSchema).mutation(async ({ ctx, input }) => {
    if (!ctx.application.oemEnabled) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用未启用OEM功能',
      })
    }
    return await ClientVersionService.createClientVersion(input, ctx.application)
  }),

  // 用户获取自己应用的客户端版本列表
  list: applicationProcedure.input(ClientVersionListSchema).query(({ input }) => {
    return ClientVersionService.getClientVersionList(input)
  }),

  // 获取公用最新版本
  getLatestPublicVersion: applicationProcedure
    .input(z.object({ platform: PlatformSchema }))
    .query(async ({ input, ctx }) => {
      return ctx.db.version.findFirst({
        where: {
          type: 'DESKTOP',
          platform: input.platform,
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
      })
    }),

  // 用户删除自己的客户端版本
  delete: applicationProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    return await ClientVersionService.deleteClientVersion(input.id, ctx.application.id)
  }),

  // 用户获取自己的客户端版本详情
  getById: applicationProcedure.input(z.object({ id: z.string() })).query(async ({ input, ctx }) => {
    return await ClientVersionService.getClientVersionById(input.id, ctx.application.id)
  }),

  // 更新应用的OEM配置
  updateOEMConfig: applicationProcedure.input(OEMConfigSchema).mutation(async ({ input }) => {
    const { applicationId, ...config } = input
    // 更新OEM配置
    return await ApplicationService.updateApplication(applicationId, {
      oemConfig: config,
    })
  }),
})
