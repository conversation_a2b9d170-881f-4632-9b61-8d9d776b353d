import crypto from 'crypto'
import { db } from '@coozf/db'
import { generateSecret, hashSecret } from '@/lib/utils/crypto'
import { Prisma } from '@prisma/client'
import { AdminApplicationListParams, OEMConfig, PaginationParams } from '@coozf/zod'
import { paginate } from '../utils'

/**
 * 应用管理业务逻辑
 */
export class ApplicationService {
  /**
   * 生成应用ID
   * 格式：app_ + 16位随机字符串
   */
  static generateAppId(): string {
    const randomString = crypto.randomBytes(8).toString('hex') // 16位十六进制字符
    return `app_${randomString}`
  }

  /**
   * 生成新的 Secret 并返回明文和哈希值
   */
  static async generateAndHashSecret(): Promise<{ secret: string; hashedSecret: string }> {
    const secret = `sk_${generateSecret()}`
    const hashedSecret = await hashSecret(secret)
    return { secret, hashedSecret }
  }

  /**
   * 确保生成唯一的应用ID
   * 如果生成的ID已存在，会重试最多5次
   */
  static async generateUniqueAppId(): Promise<string> {
    const maxRetries = 5

    for (let i = 0; i < maxRetries; i++) {
      const appId = this.generateAppId()

      // 检查是否已存在
      const existing = await db.application.findMany({
        where: { appId },
        take: 1,
      })

      if (existing.length === 0) {
        return appId
      }
    }

    throw new Error('无法生成唯一的应用ID，请稍后重试')
  }

  /**
   * 重新生成应用密钥
   */
  static async regenerateSecret(applicationId: string) {
    const { secret, hashedSecret } = await this.generateAndHashSecret()

    await db.application.update({
      where: { id: applicationId },
      data: { secret: hashedSecret },
    })

    return secret // 返回新的明文密钥
  }

  /**
   * 获取应用列表（管理员用，支持分页和搜索）
   */
  static async getApplicationList(params: AdminApplicationListParams & PaginationParams & { isUser?: boolean }) {
    const { page, pageSize, search, userId, isUser } = params
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.ApplicationWhereInput = {}

    if (search) {
      where.name = { contains: search }
    }
    if (userId) {
      where.userId = userId
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => db.application.count({ where }),
        getItems: (skip, take) =>
          db.application.findMany({
            where,
            skip,
            take,
            orderBy: { createdAt: 'desc' },
            include: {
              user: isUser
                ? {
                    select: {
                      id: true,
                      name: true,
                      image: true,
                      phoneNumber: true,
                    },
                  }
                : false,
              _count: {
                select: {
                  authAccounts: true,
                },
              },
            },
          }),
        transform: (app) => ({
          ...app,
          trafficQuotaGB: app.trafficQuotaGB.toNumber(),
          trafficUsedGB: app.trafficUsedGB.toNumber(),
          remainingTrafficGB: app.trafficQuotaGB.sub(app.trafficUsedGB).toNumber(),
          authAccountsCount: app._count.authAccounts,
        }),
      },
    )
  }

  /**
   * 获取应用统计数据
   */
  static async getApplicationStats(applicationId: string) {
    // 获取API调用统计
    const apiCalls = await db.apiCall.findMany({
      where: {
        applicationId,
      },
    })

    let accountQuotaCount = 0
    let trafficGB = 0

    apiCalls.forEach((call) => {
      if (call.costType === 'ACCOUNT_QUOTA') {
        accountQuotaCount += 1
      }
      if (call.costType === 'TRAFFIC') {
        trafficGB += Number(call.costAmount)
      }
    })

    // 获取订单总额（已完成的订单）
    const orders = await db.order.findMany({
      where: {
        applicationId,
        status: 'COMPLETED',
      },
    })

    const totalOrderAmount = orders.reduce((sum, order) => {
      return sum + Number(order.amount)
    }, 0)

    return {
      apiCallCount: apiCalls.length,
      accountCount: accountQuotaCount,
      trafficGB,
      totalOrderAmount,
    }
  }

  /**
   * 根据ID和用户ID获取应用
   */
  static async getApplicationByIdAndUserId(applicationId: string, userId: string) {
    return await db.application.findFirst({
      where: {
        id: applicationId,
        userId: userId,
      },
    })
  }

  /**
   * 更新应用
   */
  static async updateApplication(id: string, data: Prisma.ApplicationUpdateInput) {
    return await db.application.update({
      where: { id },
      data,
    })
  }
}

/**
 * 格式化流量显示
 */
export function formatTraffic(trafficKB: number): string {
  if (trafficKB < 1024) {
    return `${trafficKB} KB`
  } else if (trafficKB < 1024 * 1024) {
    return `${(trafficKB / 1024).toFixed(2)} MB`
  } else {
    return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
  }
}
